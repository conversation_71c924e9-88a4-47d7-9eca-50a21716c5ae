{"millis": "毫秒", "seconds": "秒", "mins": "分钟", "Back": "返回", "Close": "关闭", "Cancel": "取消", "Confirm": "确认", "Maximize": "最大化", "Minimize": "最小化", "Format document": "格式化文档", "Empty": "空空如也", "New": "新建", "Edit": "编辑", "Save": "保存", "Delete": "删除", "Enable": "启用", "Disable": "禁用", "Label-Home": "首 页", "Label-Proxies": "代 理", "Label-Profiles": "订 阅", "Label-Connections": "连 接", "Label-Rules": "规 则", "Label-Logs": "日 志", "Label-Unlock": "测 试", "Label-Settings": "设 置", "Proxies": "代理", "Proxy Groups": "代理组", "Proxy Provider": "代理集合", "Proxy Count": "节点数量", "Update All": "更新全部", "Update At": "更新于", "rule": "规则", "global": "全局", "direct": "直连", "script": "脚本", "locate": "当前节点", "Delay check": "延迟测试", "Sort by default": "默认排序", "Sort by delay": "按延迟排序", "Sort by name": "按名称排序", "Delay check URL": "延迟测试链接", "Delay check to cancel fixed": "进行延迟测试，以取消固定", "Proxy basic": "隐藏节点细节", "Proxy detail": "展示节点细节", "Profiles": "订阅", "Update All Profiles": "更新所有订阅", "View Runtime Config": "查看运行时订阅", "Reactivate Profiles": "重新激活订阅", "Paste": "粘贴", "Profile URL": "订阅文件链接", "Import": "导入", "From": "来自", "Update Time": "更新时间", "Used / Total": "已使用 / 总量", "Expire Time": "到期时间", "Create Profile": "新建配置", "Edit Profile": "编辑配置", "Edit Proxies": "编辑节点", "Use newlines for multiple uri": "多条 URI 请使用换行分隔（支持 Base64 编码）", "Edit Rules": "编辑规则", "Rule Type": "规则类型", "Rule Content": "规则内容", "Proxy Policy": "代理策略", "No Resolve": "跳过 DNS 解析", "Prepend Rule": "添加前置规则", "Append Rule": "添加后置规则", "Prepend Group": "添加前置代理组", "Append Group": "添加后置代理组", "Prepend Proxy": "添加前置代理节点", "Append Proxy": "添加后置代理节点", "Rule Condition Required": "规则条件缺失", "Invalid Rule": "无效规则", "Advanced": "高级", "Visualization": "可视化", "DOMAIN": "匹配完整域名", "DOMAIN-SUFFIX": "匹配域名后缀", "DOMAIN-KEYWORD": "匹配域名关键字", "DOMAIN-REGEX": "匹配域名正则表达式", "GEOSITE": "匹配 Geosite 内的域名", "GEOIP": "匹配 IP 所属国家代码", "SRC-GEOIP": "匹配来源 IP 所属国家代码", "IP-ASN": "匹配 IP 所属 ASN", "SRC-IP-ASN": "匹配来源 IP 所属 ASN", "IP-CIDR": "匹配 IP 地址范围", "IP-CIDR6": "匹配 IP 地址范围", "SRC-IP-CIDR": "匹配来源 IP 地址范围", "IP-SUFFIX": "匹配 IP 后缀范围", "SRC-IP-SUFFIX": "匹配来源 IP 后缀范围", "SRC-PORT": "匹配请求来源端口范围", "DST-PORT": "匹配请求目标端口范围", "IN-PORT": "匹配入站端口", "DSCP": "DSCP标记（仅限 TPROXY UDP 入站）", "PROCESS-NAME": "匹配进程名称（Android 包名）", "PROCESS-PATH": "匹配完整进程路径", "PROCESS-NAME-REGEX": "正则匹配完整进程名称（Android 包名）", "PROCESS-PATH-REGEX": "正则匹配完整进程路径", "NETWORK": "匹配传输协议 (TCP/UDP)", "UID": "匹配 Linux USER ID", "IN-TYPE": "匹配入站类型", "IN-USER": "匹配入站用户名", "IN-NAME": "匹配入站名称", "SUB-RULE": "子规则", "RULE-SET": "匹配规则集", "AND": "逻辑和", "OR": "逻辑或", "NOT": "逻辑非", "MATCH": "匹配所有请求", "DIRECT": "直连", "REJECT": "拦截请求", "REJECT-DROP": "抛弃请求", "PASS": "跳过此规则", "Edit Groups": "编辑代理组", "Group Type": "代理组类型", "select": "手动选择代理", "url-test": "根据URL测试延迟选择代理", "fallback": "不可用时切换到另一个代理", "load-balance": "根据负载均衡分配代理", "relay": "根据定义的代理链传递", "Group Name": "代理组组名", "Use Proxies": "引入代理", "Use Provider": "引入代理集合", "Health Check Url": "健康检查测试地址", "Expected Status": "期望状态码", "Interval": "检查间隔", "Lazy": "懒惰状态", "Timeout": "超时时间", "Max Failed Times": "最大失败次数", "Interface Name": "出站接口", "Routing Mark": "路由标记", "Include All": "引入所有出站代理、代理集合", "Include All Providers": "引入所有代理集合", "Include All Proxies": "引入所有出站代理", "Exclude Filter": "排除节点", "Exclude Type": "排除节点类型", "Disable UDP": "禁用 UDP", "Hidden": "隐藏代理组", "Group Name Required": "代理组名称不能为空", "Group Name Already Exists": "代理组名称已存在", "Extend Config": "扩展覆写配置", "Extend Script": "扩展脚本", "Global Merge": "全局扩展覆写配置", "Global Script": "全局扩展脚本", "Type": "类型", "Name": "名称", "Descriptions": "描述", "Subscription URL": "订阅链接", "Update Interval": "更新间隔", "Choose File": "选择文件", "Use System Proxy": "使用系统代理更新", "Use Clash Proxy": "使用内核代理更新", "Accept Invalid Certs (Danger)": "允许无效证书（危险）", "Refresh": "刷新", "Home": "首页", "Select": "使用", "Edit Info": "编辑信息", "Edit File": "编辑文件", "Open File": "打开文件", "Update": "更新", "Update via proxy": "更新（代理）", "Update(Proxy)": "更新（代理）", "Confirm deletion": "确认删除", "This operation is not reversible": "此操作不可逆", "Script Console": "脚本控制台输出", "To Top": "移到最前", "To End": "移到末尾", "Connections": "连接", "Table View": "表格视图", "List View": "列表视图", "Close All": "关闭全部", "Upload": "上传", "Download": "下载", "Download Speed": "下载速度", "Upload Speed": "上传速度", "Host": "主机", "Downloaded": "下载量", "Uploaded": "上传量", "DL Speed": "下载速度", "UL Speed": "上传速度", "Active Connections": "活跃连接", "Chains": "链路", "Rule": "规则", "Process": "进程", "Time": "连接时间", "Source": "源地址", "Destination": "目标地址", "DestinationPort": "目标端口", "Close Connection": "关闭连接", "Rules": "规则", "Rule Provider": "规则集合", "Logs": "日志", "Pause": "暂停", "Resume": "继续", "Clear": "清除", "Test": "测试", "Test All": "测试全部", "Testing...": "测试中...", "Create Test": "新建测试", "Edit Test": "编辑测试", "Icon": "图标", "Test URL": "测试地址", "Settings": "设置", "System Setting": "系统设置", "Tun Mode": "虚拟网卡模式", "TUN requires Service Mode": "TUN 模式需要安装服务", "Install Service": "安装服务", "Install Service failed": "安装服务失败", "Uninstall Service": "卸载服务", "Restart Core failed": "重启核心失败", "Reset to Default": "重置为默认值", "Tun Mode Info": "TUN（虚拟网卡）模式接管系统所有流量，启用时无须打开系统代理", "TUN requires Service Mode or Admin Mode": "TUN 模式需要安装服务模式或管理员模式", "System Proxy Enabled": "系统代理已启用，您的应用将通过代理访问网络", "System Proxy Disabled": "系统代理已关闭，建议大多数用户打开此选项", "TUN Mode Enabled": "TUN 模式已启用，应用将通过虚拟网卡访问网络", "TUN Mode Disabled": "TUN 模式已关闭，适用于特殊应用", "TUN Mode Service Required": "TUN模式需要服务模式，请先安装服务", "TUN Mode Intercept Info": "TUN模式可以接管所有应用流量，适用于特殊不遵循系统代理设置的应用", "Core communication error": "内核通信错误", "Rule Mode Description": "基于预设规则智能判断流量走向，提供灵活的代理策略", "Global Mode Description": "所有流量均通过代理服务器，适用于需要全局科学上网的场景", "Direct Mode Description": "所有流量不经过代理节点，但经过Clash内核转发连接目标服务器，适用于需要通过内核进行分流的特定场景", "Stack": "TUN 模式堆栈", "System and Mixed Can Only be Used in Service Mode": "System 和 Mixed 只能在服务模式下使用", "Device": "TUN 网卡名称", "Auto Route": "自动设置全局路由", "Strict Route": "严格路由", "Auto Detect Interface": "自动选择流量出口接口", "DNS Hijack": "DNS 劫持", "MTU": "最大传输单元", "Service Mode": "服务模式", "Service Mode Info": "启用 TUN 模式前请安装服务模式，该服务启动的内核进程可获得安装虚拟网卡（TUN 模式）的权限", "Current State": "当前状态", "pending": "等待中", "installed": "已安装", "uninstall": "未安装", "active": "已激活", "unknown": "未知", "Information: Please make sure that the Clash Verge Service is installed and enabled": "提示信息: 请确保 Clash Verge Service 已安装并启用", "Install": "安装", "Uninstall": "卸载", "Disable Service Mode": "禁用服务模式", "System Proxy": "系统代理", "System Proxy Info": "修改操作系统的代理设置，如果开启失败，可手动修改操作系统的代理设置", "System Proxy Setting": "系统代理设置", "Current System Proxy": "当前系统代理", "Enable status": "开启状态：", "Enabled": "已启用", "Disabled": "未启用", "Server Addr": "服务地址：", "Proxy Host": "代理主机", "Invalid Proxy Host Format": "代理主机格式无效", "Not available": "不可用", "Proxy Guard": "系统代理守卫", "Proxy Guard Info": "开启以防止其他软件修改操作系统的代理设置", "Guard Duration": "代理守卫间隔", "Always use Default Bypass": "始终使用默认绕过", "Use Bypass Check": "启用代理绕过检查", "Proxy Bypass": "代理绕过设置：", "Bypass": "当前绕过：", "Use PAC Mode": "使用 PAC 模式", "PAC Script Content": "PAC 脚本内容", "PAC URL": "PAC 地址：", "Auto Launch": "开机自启", "Administrator mode may not support auto launch": "管理员模式可能不支持开机自启", "Silent Start": "静默启动", "Silent Start Info": "程序启动时以后台模式运行，不显示程序面板", "Hover Jump Navigator": "悬浮跳转导航", "Hover Jump Navigator Info": "鼠标悬停在字母上时自动滚动到对应代理组", "TG Channel": "Telegram 频道", "Manual": "使用手册", "Github Repo": "GitHub 项目地址", "Clash Setting": "Clash 设置", "Allow Lan": "局域网连接", "Network Interface": "网络接口", "Ip Address": "IP 地址", "Mac Address": "MAC 地址", "IPv6": "IPv6", "Unified Delay": "统一延迟", "Unified Delay Info": "开启统一延迟时，会进行两次延迟测试，以消除连接握手等带来的不同类型节点的延迟差异", "Log Level": "日志等级", "Log Level Info": "仅对日志目录 Service 文件夹下的内核日志文件生效", "Port Config": "端口设置", "Random Port": "随机端口", "Mixed Port": "混合代理端口", "Socks Port": "SOCKS 代理端口", "Http Port": "HTTP(S) 代理端口", "Redir Port": "Redir 透明代理端口", "TPROXY Port": "TPROXY 透明代理端口", "External": "外部控制", "Enable External Controller": "启用外部控制器", "External Controller": "外部控制器监听地址", "Core Secret": "API 访问密钥", "Recommended": "建议设置", "Open URL": "打开链接", "Replace host, port, secret with %host, %port, %secret": "使用 %host, %port, %secret 表示 主机, 端口, 访问密钥", "Support %host, %port, %secret": "支持 %host, %port, %secret", "Clash Core": "Clash 内核", "Upgrade": "升级内核", "Restart": "重启内核", "Release Version": "正式版", "Alpha Version": "预览版", "Please Enable Service Mode": "请先安装并启用服务模式", "Please enter your root password": "请输入您的 root 密码", "Grant": "授权", "Open UWP tool": "UWP 工具", "Open UWP tool Info": "Windows 8 开始限制 UWP 应用（如微软商店）直接访问本地主机的网络服务，使用此工具可绕过该限制", "Update GeoData": "更新 GeoData", "Verge Basic Setting": "Verge 基础设置", "Verge Advanced Setting": "Verge 高级设置", "Language": "语言设置", "Theme Mode": "主题模式", "theme.light": "浅色", "theme.dark": "深色", "theme.system": "系统", "Tray Click Event": "托盘点击事件", "Show Main Window": "显示主窗口", "Show Tray Menu": "显示托盘菜单", "Copy Env Type": "复制环境变量类型", "Copy Success": "复制成功", "Start Page": "启动页面", "Startup Script": "启动脚本", "Browse": "浏览", "Theme Setting": "主题设置", "Primary Color": "主要颜色", "Secondary Color": "次要颜色", "Primary Text": "文本主要颜色", "Secondary Text": "文本次要颜色", "Info Color": "信息颜色", "Warning Color": "警告颜色", "Error Color": "错误颜色", "Success Color": "成功颜色", "Font Family": "字体系列", "CSS Injection": "CSS 注入", "Layout Setting": "界面设置", "Traffic Graph": "流量图显", "Memory Usage": "内核占用", "Memory Cleanup": "点击清理内存", "Proxy Group Icon": "代理组图标", "Nav Icon": "导航栏图标", "Monochrome": "单色图标", "Colorful": "彩色图标", "Tray Icon": "托盘图标", "Common Tray Icon": "常规托盘图标", "System Proxy Tray Icon": "系统代理托盘图标", "Tun Tray Icon": "TUN 模式托盘图标", "Miscellaneous": "杂项设置", "App Log Level": "应用日志等级", "Auto Close Connections": "自动关闭连接", "Auto Close Connections Info": "当代理组选中节点或代理模式变动时，关闭已建立的连接", "Auto Check Update": "自动检查更新", "Enable Builtin Enhanced": "内置增强功能", "Enable Builtin Enhanced Info": "配置文件的兼容性处理", "Proxy Layout Columns": "代理页布局列数", "Auto Columns": "自动列数", "Auto Log Clean": "自动清理日志", "Never Clean": "不清理", "Retain _n Days": "保留 {{n}} 天", "Default Latency Test": "默认测试链接", "Default Latency Test Info": "仅用于 HTTP 客户端请求测试，不会对配置文件产生影响", "Default Latency Timeout": "测试超时时间", "Hotkey Setting": "热键设置", "Enable Global Hotkey": "启用全局热键", "open_or_close_dashboard": "打开/关闭面板", "clash_mode_rule": "规则模式", "clash_mode_global": "全局模式", "clash_mode_direct": "直连模式", "toggle_system_proxy": "打开/关闭系统代理", "toggle_tun_mode": "打开/关闭 TUN 模式", "entry_lightweight_mode": "进入轻量模式", "Backup Setting": "备份设置", "Backup Setting Info": "支持 WebDAV 备份配置文件", "Runtime Config": "当前配置", "Open Conf Dir": "配置目录", "Open Conf Dir Info": "如果软件运行异常，!备份!并删除此文件夹下的所有文件，重启软件", "Open Core Dir": "内核目录", "Open Logs Dir": "日志目录", "Check for Updates": "检查更新", "Go to Release Page": "前往发布页", "Portable Updater Error": "便携版不支持应用内更新，请手动下载替换", "Break Change Update Error": "此版本为重大更新，不支持应用内更新，请卸载后手动下载安装", "Open Dev Tools": "开发者工具", "Export Diagnostic Info": "导出诊断信息", "Export Diagnostic Info For Issue Reporting": "导出诊断信息用于问题报告", "Exit": "退出", "Verge Version": "Verge <PERSON>", "ReadOnly": "只读", "ReadOnlyMessage": "无法在只读模式下编辑", "Filter": "过滤节点", "Filter conditions": "过滤条件", "Match Case": "区分大小写", "Match Whole Word": "全字匹配", "Use Regular Expression": "使用正则表达式", "Profile Imported Successfully": "导入订阅成功", "Profile Switched": "订阅已切换", "Profile Reactivated": "订阅已激活", "Profile switch interrupted by new selection": "配置切换被新选择中断", "Only YAML Files Supported": "仅支持 YAML 文件", "Settings Applied": "设置已应用", "Stopping Core...": "停止内核中...", "Restarting Core...": "重启内核中...", "Installing Service...": "安装服务中...", "Uninstalling Service...": "卸载服务中...", "Service Installed Successfully": "已成功安装服务", "Service Uninstalled Successfully": "已成功卸载服务", "Waiting for service to be ready...": "等待服务准备就绪...", "Service not ready, retrying attempt {count}/{total}...": "服务未就绪，正在重试 {{count}}/{{total}} 次...", "Failed to check service status, retrying attempt {count}/{total}...": "检查服务状态失败，正在重试 {{count}}/{{total}} 次...", "Service did not become ready after attempts. Proceeding with core restart.": "服务在尝试后仍未就绪。正在重启内核。", "Service was ready, but core restart might have issues or service became unavailable. Please check.": "服务已就绪，但内核重启可能存在问题或服务变得不可用。请检查。", "Service installation or core restart encountered issues. Service might not be available. Please check system logs.": "服务安装或内核重启遇到问题。服务可能不可用。请检查系统日志。", "Attempting to restart core as a fallback...": "尝试重启内核作为后备方案...", "Fallback core restart also failed: {message}": "后备内核重启也失败了: {{message}}", "Service is ready and core restarted": "服务已就绪，内核已重启", "Core restarted. Service is now available.": "内核已重启，服务现已可用", "Proxy Daemon Duration Cannot be Less than 1 Second": "代理守护间隔时间不得低于 1 秒", "Invalid Bypass Format": "无效的代理绕过格式", "Clash Port Modified": "Clash 端口已修改", "Port Conflict": "端口冲突", "Restart Application to Apply Modifications": "重启 Verge 以应用修改", "External Controller Address Modified": "外部控制器监听地址已修改", "Permissions Granted Successfully for _clash Core": "{{core}} 内核授权成功", "Core Version Updated": "内核版本已更新", "Clash Core Restarted": "已重启 Clash 内核", "GeoData Updated": "已更新 GeoData", "Currently on the Latest Version": "当前已是最新版本", "Already Using Latest Core": "已是最新内核版本", "Import Subscription Successful": "导入订阅成功", "WebDAV Server URL": "WebDAV 服务器地址 http(s)://", "Username": "用户名", "Password": "密码", "Backup": "备份", "Filename": "文件名称", "Actions": "操作", "Restore": "恢复", "No Backups": "暂无备份", "WebDAV URL Required": "WebDAV 服务器地址不能为空", "Invalid WebDAV URL": "无效的 WebDAV 服务器地址格式", "Username Required": "用户名不能为空", "Password Required": "密码不能为空", "Failed to Fetch Backups": "获取备份文件失败", "WebDAV Config Saved": "WebDAV 配置保存成功", "WebDAV Config Save Failed": "保存 WebDAV 配置失败: {{error}}", "Backup Created": "备份创建成功", "Backup Failed": "备份失败: {{error}}", "Delete Backup": "删除备份", "Restore Backup": "恢复备份", "Backup Time": "备份时间", "Confirm to delete this backup file?": "确认删除此备份文件吗？", "Confirm to restore this backup file?": "确认恢复此份文件吗？", "Restore Success, App will restart in 1s": "恢复成功，应用将在 1 秒后重启", "Failed to fetch backup files": "获取备份文件失败", "Profile": "配置", "Help": "帮助", "About": "关于", "Theme": "主题", "Main Window": "主窗口", "Group Icon": "分组图标", "Menu Icon": "菜单图标", "PAC File": "PAC 文件", "Web UI": "网页界面", "Hotkeys": "快捷键", "Verge Mixed Port": "Verge 混合端口", "Verge Socks Port": "Verge SOCKS 端口", "Verge Redir Port": "Verge 重定向端口", "Verge Tproxy Port": "Verge 透明代理端口", "Verge Port": "Verge 端口", "Verge HTTP Enabled": "Verge HTTP 已启用", "WebDAV URL": "WebDAV 地址", "WebDAV Username": "WebDAV 用户名", "WebDAV Password": "WebDAV 密码", "Dashboard": "仪表板", "Restart App": "重启应用", "Restart Clash Core": "重启 Clash 核心", "TUN Mode": "TUN 模式", "Copy Env": "复制环境变量", "Conf Dir": "配置目录", "Core Dir": "核心目录", "Logs Dir": "日志目录", "Open Dir": "打开目录", "More": "更多", "Rule Mode": "规则模式", "Global Mode": "全局模式", "Direct Mode": "直连模式", "Enable Tray Speed": "启用托盘速率", "Enable Tray Icon": "启用托盘图标", "LightWeight Mode": "轻量模式", "LightWeight Mode Info": "关闭GUI界面，仅保留内核运行", "LightWeight Mode Settings": "轻量模式设置", "Enter LightWeight Mode Now": "立即进入轻量模式", "Auto Enter LightWeight Mode": "自动进入轻量模式", "Auto Enter LightWeight Mode Info": "启用后，将在窗口关闭一段时间后自动激活轻量模式", "Auto Enter LightWeight Mode Delay": "自动进入轻量模式延迟", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "关闭窗口后，轻量模式将在 {{n}} 分钟后自动激活", "Config Validation Failed": "订阅配置校验失败，请检查订阅配置文件，变更已撤销，错误详情：", "Boot Config Validation Failed": "启动订阅配置校验失败，已使用默认配置启动；请检查订阅配置文件，错误详情：", "Core Change Config Validation Failed": "切换内核时配置校验失败，已使用默认配置启动；请检查订阅配置文件，错误详情：", "Config Validation Process Terminated": "验证进程被终止", "Script Syntax Error": "脚本语法错误，变更已撤销", "Script Missing Main": "脚本错误，变更已撤销", "File Not Found": "文件丢失，变更已撤销", "Script File Error": "脚本文件错误，变更已撤销", "Core Changed Successfully": "内核切换成功", "Failed to Change Core": "无法切换内核", "YAML Syntax Error": "YAML语法错误，变更已撤销", "YAML Read Error": "YAML读取错误，变更已撤销", "YAML Mapping Error": "YAML映射错误，变更已撤销", "YAML Key Error": "YAML键错误，变更已撤销", "YAML Error": "YAML错误，变更已撤销", "Merge File Syntax Error": "覆写文件语法错误，变更已撤销", "Merge File Mapping Error": "覆写文件映射错误，变更已撤销", "Merge File Key Error": "覆写文件键错误，变更已撤销", "Merge File Error": "覆写文件错误，变更已撤销", "Validate YAML File": "验证YAML文件", "Validate Merge File": "验证覆写文件", "Validation Success": "验证成功", "Validation Failed": "验证失败", "Service Administrator Prompt": "Clash Verge 需要管理员权限安装系统服务", "DNS Settings": "DNS 设置", "DNS settings saved": "DNS 设置已保存", "DNS Overwrite": "DNS 覆写", "DNS Settings Warning": "如果你不清楚这里的设置请不要修改，并保持 DNS 覆写开启", "Enable DNS": "启用 DNS", "DNS Listen": "DNS 监听地址", "Enhanced Mode": "增强模式", "Fake IP Range": "Fake IP 范围", "Fake IP Filter Mode": "Fake IP 过滤模式", "Enable IPv6 DNS resolution": "启用 IPv6 DNS 解析", "Prefer H3": "优先使用 HTTP/3", "DNS DOH使用HTTP/3": "DNS DOH 使用 HTTP/3 协议", "Respect Rules": "遵循路由规则", "DNS connections follow routing rules": "DNS 连接遵循路由规则", "Use Hosts": "使用 Hosts", "Enable to resolve hosts through hosts file": "启用通过 hosts 文件解析域名", "Use System Hosts": "使用系统 Hosts", "Enable to resolve hosts through system hosts file": "启用通过系统 hosts 文件解析域名", "Direct Nameserver Follow Policy": "直连域名服务器遵循策略", "Whether to follow nameserver policy": "是否遵循 nameserver-policy 设置", "Default Nameserver": "默认域名服务器", "Default DNS servers used to resolve DNS servers": "用于解析 DNS 服务器的默认 DNS 服务器", "Nameserver": "域名服务器", "List of DNS servers": "DNS 服务器列表，用逗号分隔", "Fallback": "回退服务器", "List of fallback DNS servers": "回退 DNS 服务器列表，用逗号分隔", "Proxy Server Nameserver": "代理节点DNS", "Proxy Node Nameserver": "代理节点域名解析服务器，仅用于解析代理节点的域名，用逗号分隔", "Direct Nameserver": "直连域名服务器", "Direct outbound Nameserver": "直连出口域名解析服务器，支持 system 关键字，用逗号分隔", "Fake IP Filter": "Fake IP 过滤", "Domains that skip fake IP resolution": "跳过 Fake IP 解析的域名，用逗号分隔", "Nameserver Policy": "域名服务器策略", "Domain-specific DNS server": "特定域名的 DNS 服务器，多个服务器使用分号分隔，格式: domain=server1;server2", "Fallback Filter Settings": "回退过滤设置", "GeoIP Filtering": "GeoIP 过滤", "Enable GeoIP filtering for fallback": "启用 GeoIP 回退过滤", "GeoIP Code": "GeoIP 国家代码", "Fallback IP CIDR": "回退 IP CIDR", "IP CIDRs not using fallback servers": "不使用回退服务器的 IP CIDR，用逗号分隔", "Fallback Domain": "回退域名", "Domains using fallback servers": "使用回退服务器的域名，用逗号分隔", "Hosts Settings": "Hosts 设置", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "自定义域名到 IP 或域名的映射，用逗号分隔", "Enable Alpha Channel": "启用 Alpha 通道", "Alpha versions may contain experimental features and bugs": "Alpha 版本可能包含实验性功能和已知问题", "Home Settings": "首页设置", "Profile Card": "订阅卡", "Current Proxy Card": "当前代理卡", "Network Settings Card": "网络设置卡", "Proxy Mode Card": "代理模式卡", "Clash Mode Card": "Clash 模式卡", "Traffic Stats Card": "流量统计卡", "Clash Info Cards": "Clash 信息卡", "System Info Cards": "系统信息卡", "Website Tests Card": "网站测试卡", "Traffic Stats": "流量统计", "Website Tests": "网站测试", "Clash Info": "Clash 信息", "Core Version": "内核版本", "System Proxy Address": "系统代理地址", "Uptime": "运行时间", "Rules Count": "规则数量", "System Info": "系统信息", "OS Info": "操作系统信息", "Running Mode": "运行模式", "Sidecar Mode": "用户模式", "Administrator Mode": "管理员模式", "Administrator + Service Mode": "管理员 + 服务模式", "Last Check Update": "最后检查更新", "Click to import subscription": "点击导入订阅", "Last Update failed": "上次更新失败", "Next Up": "下次更新", "No schedule": "没有计划", "Unknown": "未知", "Auto update disabled": "自动更新已禁用", "Update subscription successfully": "订阅更新成功", "Update failed, retrying with Clash proxy...": "订阅更新失败，尝试使用 Clash 代理更新", "Update with Clash proxy successfully": "使用 Clash 代理更新成功", "Update failed even with Clash proxy": "使用 Clash 代理更新也失败", "Profile creation failed, retrying with Clash proxy...": "订阅创建失败，尝试使用 Clash 代理创建", "Profile creation succeeded with Clash proxy": "使用 Clash 代理创建订阅成功", "Import failed, retrying with Clash proxy...": "订阅导入失败，尝试使用 Clash 代理导入", "Profile Imported with Clash proxy": "使用 Clash 代理导入订阅成功", "Import failed even with Clash proxy": "使用 Clash 代理导入订阅也失败", "Current Node": "当前节点", "No active proxy node": "暂无激活的代理节点", "Network Settings": "网络设置", "Proxy Mode": "代理模式", "Group": "代理组", "Proxy": "节点", "IP Information Card": "IP 信息卡", "IP Information": "IP 信息", "Failed to get IP info": "获取IP信息失败", "ISP": "服务商", "ASN": "自治域", "ORG": "组织", "Location": "位置", "Timezone": "时区", "Auto refresh": "自动刷新", "Unlock Test": "解锁测试", "Pending": "待检测", "Yes": "支持", "No": "不支持", "Failed": "测试失败", "Completed": "检测完成", "Disallowed ISP": "不允许的 ISP", "Originals Only": "仅限原创", "No (IP Banned By Disney+)": "不支持（IP被Disney+禁止）", "Unsupported Country/Region": "不支持的国家/地区", "Failed (Network Connection)": "测试失败（网络连接问题）", "DashboardToggledTitle": "仪表盘已切换", "DashboardToggledBody": "已通过快捷键切换仪表盘显示状态", "ClashModeChangedTitle": "Clash 模式切换", "ClashModeChangedBody": "已切换为 {mode} 模式", "SystemProxyToggledTitle": "系统代理切换", "SystemProxyToggledBody": "已通过快捷键切换系统代理状态", "TunModeToggledTitle": "TUN 模式切换", "TunModeToggledBody": "已通过快捷键切换 TUN 模式", "LightweightModeEnteredTitle": "轻量模式", "LightweightModeEnteredBody": "已通过快捷键进入轻量模式", "AppQuitTitle": "应用退出", "AppQuitBody": "已通过快捷键退出应用", "AppHiddenTitle": "应用隐藏", "AppHiddenBody": "已通过快捷键隐藏应用窗口", "Invalid Profile URL": "无效的订阅链接，请输入以 http:// 或 https:// 开头的地址", "Saved Successfully": "保存成功", "External Cors": "外部控制跨域", "Enable one-click CORS for external API. Click to toggle CORS": "设置内核跨域访问，点击切换 CORS是否启用", "External Cors Settings": "外部控制跨域设置", "External Cors Configuration": "外部控制跨域配置", "Allow private network access": "允许专用网络访问", "Allowed Origins": "允许的来源", "Please enter a valid url": "请输入有效的网址", "Add": "添加", "Always included origins: {{urls}}": "始终包含来源：{{urls}}", "Invalid regular expression": "无效的正则表达式", "Copy Version": "复制Verge版本号", "Version copied to clipboard": "Verge版本已复制到剪贴板", "Controller address cannot be empty": "控制器地址不能为空", "Secret cannot be empty": "访问密钥不能为空", "Configuration saved successfully": "配置保存成功", "Failed to save configuration": "配置保存失败", "Controller address copied to clipboard": "控制器地址已复制到剪贴板", "Secret copied to clipboard": "访问密钥已复制到剪贴板", "Saving...": "保存中..."}