{"millis": "ミリ秒", "seconds": "秒", "mins": "分", "Back": "戻る", "Close": "閉じる", "Cancel": "キャンセル", "Confirm": "確認", "Maximize": "最大化", "Minimize": "最小化", "Format document": "文書を整形する", "Empty": "空っぽ", "New": "新規作成", "Edit": "編集", "Save": "保存", "Delete": "削除", "Enable": "有効にする", "Disable": "無効にする", "Label-Home": "ホーム", "Label-Proxies": "プロキシ", "Label-Profiles": "プロファイル", "Label-Connections": "接続", "Label-Rules": "ルール", "Label-Logs": "ログ", "Label-Unlock": "テスト", "Label-Settings": "設定", "Proxy Groups": "プロキシグループ", "Proxy Provider": "プロキシプロバイダー", "Proxy Count": "ノード数", "Update All": "すべて更新", "Update At": "更新日時", "rule": "ルール", "global": "グローバル", "direct": "直接接続", "script": "スクリプト", "locate": "現在のノード", "Delay check": "遅延テスト", "Sort by default": "デフォルトでソート", "Sort by delay": "遅延でソート", "Sort by name": "名前でソート", "Delay check URL": "遅延テストURL", "Delay check to cancel fixed": "遅延テストを実行して固定を解除する", "Proxy basic": "ノードの詳細を隠す", "Proxy detail": "ノードの詳細を表示する", "Profiles": "プロファイル", "Update All Profiles": "すべてのプロファイルを更新", "View Runtime Config": "実行時のプロファイルを表示", "Reactivate Profiles": "プロファイルを再アクティブ化", "Paste": "貼り付け", "Profile URL": "プロファイルファイルのURL", "Import": "インポート", "From": "から", "Update Time": "更新時間", "Used / Total": "使用済み / 合計", "Expire Time": "有効期限", "Create Profile": "新規プロファイルを作成", "Edit Profile": "プロファイルを編集", "Edit Proxies": "ノードを編集", "Use newlines for multiple uri": "複数のURIは改行で区切ってください（Base64エンコードに対応）", "Edit Rules": "ルールを編集", "Rule Type": "ルールタイプ", "Rule Content": "ルール内容", "Proxy Policy": "プロキシポリシー", "No Resolve": "DNS解決をスキップ", "Prepend Rule": "前置ルールを追加", "Append Rule": "後置ルールを追加", "Prepend Group": "前置プロキシグループを追加", "Append Group": "後置プロキシグループを追加", "Prepend Proxy": "前置プロキシノードを追加", "Append Proxy": "後置プロキシノードを追加", "Rule Condition Required": "ルール条件が必要です", "Invalid Rule": "無効なルール", "Advanced": "詳細設定", "Visualization": "可視化", "DOMAIN": "完全なドメイン名を一致させる", "DOMAIN-SUFFIX": "ドメインサフィックスを一致させる", "DOMAIN-KEYWORD": "ドメインキーワードを一致させる", "DOMAIN-REGEX": "ドメイン正規表現を一致させる", "GEOSITE": "Geosite内のドメインを一致させる", "GEOIP": "IPの所属国コードを一致させる", "SRC-GEOIP": "送信元IPの所属国コードを一致させる", "IP-ASN": "IPの所属ASNを一致させる", "SRC-IP-ASN": "送信元IPの所属ASNを一致させる", "IP-CIDR": "IPアドレス範囲を一致させる", "IP-CIDR6": "IPアドレス範囲を一致させる", "SRC-IP-CIDR": "送信元IPアドレス範囲を一致させる", "IP-SUFFIX": "IPサフィックス範囲を一致させる", "SRC-IP-SUFFIX": "送信元IPサフィックス範囲を一致させる", "SRC-PORT": "送信元ポート範囲を一致させる", "DST-PORT": "宛先ポート範囲を一致させる", "IN-PORT": "入力ポートを一致させる", "DSCP": "DSCPマーク（TPROXY UDP入力のみ）", "PROCESS-NAME": "プロセス名を一致させる（Androidパッケージ名）", "PROCESS-PATH": "完全なプロセスパスを一致させる", "PROCESS-NAME-REGEX": "完全なプロセス名を正規表現で一致させる（Androidパッケージ名）", "PROCESS-PATH-REGEX": "完全なプロセスパスを正規表現で一致させる", "NETWORK": "トランスポートプロトコルを一致させる (TCP/UDP)", "UID": "LinuxユーザーIDを一致させる", "IN-TYPE": "入力タイプを一致させる", "IN-USER": "入力ユーザー名を一致させる", "IN-NAME": "入力名を一致させる", "SUB-RULE": "サブルール", "RULE-SET": "ルールセットを一致させる", "AND": "論理積", "OR": "論理和", "NOT": "論理否定", "MATCH": "すべてのリクエストを一致させる", "DIRECT": "直接接続", "REJECT": "リクエストを拒否", "REJECT-DROP": "リクエストを破棄", "PASS": "このルールをスキップ", "Edit Groups": "プロキシグループを編集", "Group Type": "プロキシグループタイプ", "select": "手動でプロキシを選択", "url-test": "URLテストによる遅延でプロキシを選択", "fallback": "利用不可の場合は別のプロキシに切り替える", "load-balance": "負荷分散によりプロキシを割り当てる", "relay": "定義されたプロキシチェーンに沿って転送する", "Group Name": "プロキシグループ名", "Use Proxies": "プロキシを導入", "Use Provider": "プロキシプロバイダーを導入", "Health Check Url": "ヘルスチェックURL", "Expected Status": "期待するステータスコード", "Interval": "チェック間隔", "Lazy": "遅延モード", "Timeout": "タイムアウト時間", "Max Failed Times": "最大失敗回数", "Interface Name": "出力インターフェース", "Routing Mark": "ルーティングマーク", "Include All": "すべての出力プロキシ、プロキシプロバイダーを導入", "Include All Providers": "すべてのプロキシプロバイダーを導入", "Include All Proxies": "すべての出力プロキシを導入", "Exclude Filter": "除外ノード", "Exclude Type": "除外ノードタイプ", "Disable UDP": "UDPを無効にする", "Hidden": "プロキシグループを隠す", "Group Name Required": "プロキシグループ名は必須です", "Group Name Already Exists": "プロキシグループ名はすでに存在します", "Extend Config": "拡張上書き設定", "Extend Script": "拡張スクリプト", "Type": "タイプ", "Name": "名前", "Descriptions": "説明", "Subscription URL": "サブスクリプションURL", "Update Interval": "更新間隔", "Choose File": "ファイルを選択", "Use System Proxy": "システムプロキシを使用して更新", "Use Clash Proxy": "クラッシュプロキシを使用して更新", "Refresh": "更新", "Home": "ホーム", "Select": "使用する", "Edit Info": "情報を編集", "Edit File": "ファイルを編集", "Open File": "ファイルを開く", "Update": "更新", "Confirm deletion": "削除を確認", "This operation is not reversible": "この操作は元に戻せません", "Script Console": "スクリプトコンソール出力", "Connections": "接続", "Table View": "テーブルビュー", "List View": "リストビュー", "Close All": "すべて閉じる", "Upload": "アップロード", "Download": "ダウンロード", "Download Speed": "ダウンロード速度", "Upload Speed": "アップロード速度", "Host": "ホスト", "Downloaded": "ダウンロード量", "Uploaded": "アップロード量", "DL Speed": "ダウンロード速度", "UL Speed": "アップロード速度", "Active Connections": "アクティブな接続", "Chains": "チェーン", "Rule": "ルール", "Process": "プロセス", "Time": "接続時間", "Source": "送信元アドレス", "Destination": "宛先アドレス", "DestinationPort": "宛先ポート", "Close Connection": "接続を閉じる", "Rules": "ルール", "Rule Provider": "ルールプロバイダー", "Logs": "ログ", "Pause": "一時停止", "Resume": "再開", "Clear": "クリア", "Test": "テスト", "Test All": "すべてテスト", "Testing...": "テスト中...", "Create Test": "新規テストを作成", "Edit Test": "テストを編集", "Icon": "アイコン", "Test URL": "テストURL", "Settings": "設定", "System Setting": "システム設定", "Tun Mode": "仮想ネットワークカードモード", "TUN requires Service Mode or Admin Mode": "TUNモードはサービスモードまたは管理者モードが必要です", "Install Service": "サービスをインストール", "Reset to Default": "デフォルト値にリセット", "Tun Mode Info": "TUN（仮想ネットワークカード）モードはシステムのすべてのトラフィックを制御します。有効にすると、システムプロキシを開く必要はありません。", "System Proxy Enabled": "システムプロキシが有効になっています。アプリケーションはプロキシを通じてネットワークにアクセスします。", "System Proxy Disabled": "システムプロキシが無効になっています。ほとんどのユーザーはこのオプションをオンにすることをお勧めします。", "TUN Mode Enabled": "TUNモードが有効になっています。アプリケーションは仮想ネットワークカードを通じてネットワークにアクセスします。", "TUN Mode Disabled": "TUNモードが無効になっています。特殊なアプリケーションに適しています。", "TUN Mode Service Required": "TUNモードはサービスモードが必要です。まずサービスをインストールしてください。", "TUN Mode Intercept Info": "TUNモードはすべてのアプリケーションのトラフィックを制御できます。システムプロキシ設定に従わない特殊なアプリケーションに適しています。", "Stack": "TUNモードスタック", "Device": "TUNネットワークカード名", "Auto Route": "グローバルルートを自動設定", "Strict Route": "厳格なルート", "Auto Detect Interface": "トラフィックの出口インターフェースを自動選択", "DNS Hijack": "DNSハイジャック", "MTU": "最大転送単位", "Service Mode": "サービスモード", "active": "アクティブ", "unknown": "不明", "Install": "インストール", "Uninstall": "アンインストール", "System Proxy": "システムプロキシ", "System Proxy Info": "オペレーティングシステムのプロキシ設定を変更します。有効にできない場合は、手動でオペレーティングシステムのプロキシ設定を変更してください。", "System Proxy Setting": "システムプロキシ設定", "Current System Proxy": "現在のシステムプロキシ", "Enable status": "有効状態：", "Enabled": "有効", "Disabled": "無効", "Server Addr": "サーバーアドレス：", "Proxy Host": "プロキシホスト", "Invalid Proxy Host Format": "プロキシホストの形式が無効です", "Not available": "利用不可", "Proxy Guard": "システムプロキシガード", "Proxy Guard Info": "他のソフトウェアがオペレーティングシステムのプロキシ設定を変更するのを防ぐために有効にします。", "Guard Duration": "プロキシガード間隔", "Always use Default Bypass": "常にデフォルトのバイパスを使用", "Proxy Bypass": "プロキシバイパス設定：", "Bypass": "現在のバイパス：", "Use PAC Mode": "PACモードを使用", "PAC Script Content": "PACスクリプト内容", "PAC URL": "PACアドレス：", "Auto Launch": "起動時に自動起動", "Administrator mode may not support auto launch": "管理者モードでは起動時の自動起動がサポートされない場合があります。", "Silent Start": "サイレント起動", "Silent Start Info": "アプリケーションを起動すると、バックグラウンドモードで実行され、アプリケーションパネルは表示されません。", "TG Channel": "Telegramチャンネル", "Manual": "マニュアル", "Github Repo": "GitHubリポジトリ", "Clash Setting": "Clash設定", "Allow Lan": "LAN接続を許可", "Network Interface": "ネットワークインターフェース", "Ip Address": "IPアドレス", "Mac Address": "MACアドレス", "IPv6": "IPv6", "Unified Delay": "統一遅延", "Unified Delay Info": "統一遅延を有効にすると、2回の遅延テストが行われ、接続ハンドシェイクなどによる異なるタイプのノードの遅延差を解消します。", "Log Level": "ログレベル", "Log Level Info": "ログディレクトリのServiceフォルダ内のコアログファイルにのみ適用されます。", "Port Configuration": "ポート設定", "Random Port": "ランダムポート", "Mixed Port": "混合プロキシポート", "Socks Port": "SOCKSプロキシポート", "Http Port": "HTTP(S)プロキシポート", "Redir Port": "Redir透明プロキシポート", "Tproxy Port": "TPROXY透明プロキシポート", "External": "外部制御", "External Controller": "外部コントローラーの監視アドレス", "Core Secret": "APIアクセスキー", "Recommended": "推奨設定", "Open URL": "URLを開く", "Replace host, port, secret with %host, %port, %secret": "%host, %port, %secretを使用してホスト、ポート、アクセスキーを表します。", "Support %host, %port, %secret": "%host, %port, %secretをサポートします。", "Clash Core": "Clashコア", "Upgrade": "コアをアップグレード", "Restart": "コアを再起動", "Release Version": "正式版", "Alpha Version": "プレビュー版", "Please enter your root password": "ルートパスワードを入力してください。", "Open UWP tool": "UWPツールを開く", "Open UWP tool Info": "Windows 8以降では、UWPアプリケーション（Microsoft Storeなど）がローカルホストのネットワークサービスに直接アクセスすることが制限されています。このツールを使用すると、この制限を回避できます。", "Update GeoData": "GeoDataを更新", "Verge Basic Setting": "Verge基本設定", "Verge Advanced Setting": "Verge詳細設定", "Language": "言語設定", "Theme Mode": "テーマモード", "theme.light": "ライト", "theme.dark": "ダーク", "theme.system": "システム", "Tray Click Event": "トレイアイコンクリックイベント", "Show Main Window": "メインウィンドウを表示", "Show Tray Menu": "トレイメニューを表示", "Copy Env Type": "環境変数タイプをコピー", "Copy Success": "コピー成功", "Start Page": "起動ページ", "Startup Script": "起動スクリプト", "Browse": "参照", "Theme Setting": "テーマ設定", "Primary Color": "主要色", "Secondary Color": "次要色", "Primary Text": "テキスト主要色", "Secondary Text": "テキスト次要色", "Info Color": "情報色", "Warning Color": "警告色", "Error Color": "エラー色", "Success Color": "成功色", "Font Family": "フォントファミリー", "CSS Injection": "CSSインジェクション", "Layout Setting": "レイアウト設定", "Traffic Graph": "トラフィックグラフ", "Memory Usage": "コアメモリ使用量", "Memory Cleanup": "クリックしてメモリをクリーンアップ", "Proxy Group Icon": "プロキシグループアイコン", "Nav Icon": "ナビゲーションバーアイコン", "Monochrome": "モノクロアイコン", "Colorful": "カラーアイコン", "Tray Icon": "トレイアイコン", "Common Tray Icon": "通常のトレイアイコン", "System Proxy Tray Icon": "システムプロキシトレイアイコン", "Tun Tray Icon": "TUNモードトレイアイコン", "Miscellaneous": "その他の設定", "App Log Level": "アプリケーションログレベル", "Auto Close Connections": "接続を自動的に閉じる", "Auto Close Connections Info": "プロキシグループで選択されたノードまたはプロキシモードが変更されたときに、既存の接続を閉じます。", "Auto Check Update": "自動更新チェック", "Enable Builtin Enhanced": "組み込み拡張機能を有効にする", "Enable Builtin Enhanced Info": "設定ファイルの互換性処理", "Proxy Layout Columns": "プロキシページのレイアウト列数", "Auto Columns": "自動列数", "Auto Log Clean": "ログを自動的にクリーンアップ", "Never Clean": "クリーンアップしない", "Retain _n Days": "{{n}}日間保持", "Default Latency Test": "デフォルトの遅延テストURL", "Default Latency Test Info": "HTTPクライアントリクエストテストにのみ使用され、設定ファイルには影響しません。", "Default Latency Timeout": "テストタイムアウト時間", "Hotkey Setting": "ホットキー設定", "Enable Global Hotkey": "グローバルホットキーを有効にする", "open_or_close_dashboard": "ダッシュボードを開く/閉じる", "clash_mode_rule": "ルールモード", "clash_mode_global": "グローバルモード", "clash_mode_direct": "直接接続モード", "toggle_system_proxy": "システムプロキシを開く/閉じる", "toggle_tun_mode": "TUNモードを開く/閉じる", "entry_lightweight_mode": "軽量モードに入る", "Backup Setting": "バックアップ設定", "Backup Setting Info": "WebDAVを使用した設定ファイルのバックアップをサポートします。", "Runtime Config": "現在の設定", "Open Conf Dir": "設定ディレクトリを開く", "Open Conf Dir Info": "アプリケーションが正常に動作しない場合は、このフォルダ内のすべてのファイルを!バックアップ!して削除し、アプリケーションを再起動してください。", "Open Core Dir": "コアディレクトリを開く", "Open Logs Dir": "ログディレクトリを開く", "Check for Updates": "更新を確認", "Go to Release Page": "リリースページに移動", "Portable Updater Error": "ポータブル版ではアプリケーション内での更新はサポートされていません。手動でダウンロードして置き換えてください。", "Break Change Update Error": "このバージョンは重大な更新であり、アプリケーション内での更新はサポートされていません。アンインストールしてから手動でダウンロードしてインストールしてください。", "Open Dev Tools": "開発者ツールを開く", "Export Diagnostic Info": "診断情報をエクスポート", "Exit": "終了", "Verge Version": "Vergeバージョン", "ReadOnly": "読み取り専用", "ReadOnlyMessage": "読み取り専用モードでは編集できません。", "Filter": "ノードをフィルタリング", "Filter conditions": "フィルタリング条件", "Match Case": "大文字小文字を区別する", "Match Whole Word": "完全一致", "Use Regular Expression": "正規表現を使用する", "Profile Imported Successfully": "プロファイルのインポートに成功しました。", "Profile Switched": "プロファイルが切り替えられました。", "Profile Reactivated": "プロファイルが再アクティブ化されました。", "Only YAML Files Supported": "YAMLファイルのみサポートされています。", "Settings Applied": "設定が適用されました。", "Stopping Core...": "コアを停止中...", "Restarting Core...": "コアを再起動中...", "Installing Service...": "サービスをインストール中...", "Uninstall Service": "サービスのアンインストール", "Service Installed Successfully": "サービスのインストールに成功しました。", "Service is ready and core restarted": "サービスが準備完了し、コアが再起動されました。", "Core restarted. Service is now available.": "コアが再起動され、サービスが利用可能になりました。", "Service was ready, but core restart might have issues or service became unavailable. Please check.": "サービスは準備が整っていましたが、コアの再起動に問題が発生したか、サービスが利用できなくなった可能性があります。ご確認ください。", "Service installation or core restart encountered issues. Service might not be available. Please check system logs.": "サービスのインストールまたはコアの再起動中に問題が発生しました。サービスが利用できない可能性があります。システムログを確認してください。", "Uninstalling Service...": "サービスをアンインストール中...", "Waiting for service to be ready...": "サービスの準備を待っています...", "Service Uninstalled Successfully": "サービスのアンインストールに成功しました。", "Proxy Daemon Duration Cannot be Less than 1 Second": "プロキシデーモンの間隔は1秒以上に設定する必要があります。", "Invalid Bypass Format": "無効なバイパス形式", "Core Version Updated": "コアバージョンが更新されました。", "Clash Core Restarted": "Clashコアが再起動されました。", "GeoData Updated": "GeoDataが更新されました。", "Currently on the Latest Version": "現在は最新バージョンです。", "Import Subscription Successful": "サブスクリプションのインポートに成功しました。", "WebDAV Server URL": "WebDAVサーバーのURL http(s)://", "Username": "ユーザー名", "Password": "パスワード", "Backup": "バックアップ", "Filename": "ファイル名", "Actions": "操作", "Restore": "復元", "No Backups": "バックアップがありません。", "WebDAV URL Required": "WebDAVサーバーのURLは必須です。", "Invalid WebDAV URL": "無効なWebDAVサーバーのURL形式", "Username Required": "ユーザー名は必須です。", "Password Required": "パスワードは必須です。", "WebDAV Config Saved": "WebDAV設定が保存されました。", "WebDAV Config Save Failed": "WebDAV設定の保存に失敗しました: {{error}}", "Backup Created": "バックアップが作成されました。", "Backup Failed": "バックアップに失敗しました: {{error}}", "Delete Backup": "バックアップを削除", "Restore Backup": "バックアップを復元", "Backup Time": "バックアップ時間", "Restore Success, App will restart in 1s": "復元に成功しました。アプリケーションは1秒後に再起動します。", "Failed to fetch backup files": "バックアップファイルの取得に失敗しました。", "Profile": "プロファイル", "Web UI": "Webインターフェース", "Dashboard": "ダッシュボード", "Restart App": "アプリケーションを再起動", "Restart Clash Core": "Clashコアを再起動", "TUN Mode": "TUNモード", "Copy Env": "環境変数をコピー", "Conf Dir": "設定ディレクトリ", "Core Dir": "コアディレクトリ", "Logs Dir": "ログディレクトリ", "Open Dir": "ディレクトリを開く", "More": "もっと見る", "Rule Mode": "ルールモード", "Global Mode": "グローバルモード", "Direct Mode": "直接接続モード", "Enable Tray Speed": "トレイの速度表示を有効にする", "Enable Tray Icon": "トレイアイコンを有効にする", "LightWeight Mode": "軽量モード", "LightWeight Mode Info": "GUIを閉じて、コアのみを実行します。", "LightWeight Mode Settings": "軽量モード設定", "Enter LightWeight Mode Now": "今すぐ軽量モードに入る", "Auto Enter LightWeight Mode": "自動的に軽量モードに入る", "Auto Enter LightWeight Mode Info": "有効にすると、ウィンドウを閉じてから一定時間後に自動的に軽量モードが有効になります。", "Auto Enter LightWeight Mode Delay": "自動的に軽量モードに入るまでの遅延時間", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "ウィンドウを閉じると、{{n}}分後に自動的に軽量モードが有効になります。", "Config Validation Failed": "プロファイル設定の検証に失敗しました。プロファイル設定ファイルを確認してください。変更は取り消されました。エラー詳細：", "Boot Config Validation Failed": "起動時のプロファイル設定の検証に失敗しました。デフォルト設定で起動しました。プロファイル設定ファイルを確認してください。エラー詳細：", "Core Change Config Validation Failed": "コアを切り替える際の設定検証に失敗しました。デフォルト設定で起動しました。プロファイル設定ファイルを確認してください。エラー詳細：", "Config Validation Process Terminated": "検証プロセスが中断されました。", "Script Syntax Error": "スクリプトの構文エラーがあります。変更は取り消されました。", "Script Missing Main": "スクリプトにメイン関数がありません。変更は取り消されました。", "File Not Found": "ファイルが見つかりません。変更は取り消されました。", "Script File Error": "スクリプトファイルにエラーがあります。変更は取り消されました。", "Core Changed Successfully": "コアの切り替えに成功しました。", "Failed to Change Core": "コアの切り替えに失敗しました。", "YAML Syntax Error": "YAML構文エラーがあります。変更は取り消されました。", "YAML Read Error": "YAMLファイルの読み取りエラーがあります。変更は取り消されました。", "YAML Mapping Error": "YAMLマッピングエラーがあります。変更は取り消されました。", "YAML Key Error": "YAMLキーエラーがあります。変更は取り消されました。", "YAML Error": "YAMLエラーがあります。変更は取り消されました。", "Merge File Syntax Error": "上書きファイルの構文エラーがあります。変更は取り消されました。", "Merge File Mapping Error": "上書きファイルのマッピングエラーがあります。変更は取り消されました。", "Merge File Key Error": "上書きファイルのキーエラーがあります。変更は取り消されました。", "Merge File Error": "上書きファイルにエラーがあります。変更は取り消されました。", "Service Administrator Prompt": "Clash Vergeはシステムサービスをインストールするために管理者権限が必要です。", "DNS Settings": "DNS設定", "DNS settings saved": "DNS設定が保存されました。", "DNS Overwrite": "DNS上書き", "DNS Settings Warning": "ここの設定がわからない場合は、変更しないでください。DNS上書きを有効にしたままにしてください。", "Enable DNS": "DNSを有効にする", "DNS Listen": "DNS監視アドレス", "Enhanced Mode": "拡張モード", "Fake IP Range": "Fake IP範囲", "Fake IP Filter Mode": "Fake IPフィルターモード", "Enable IPv6 DNS resolution": "IPv6 DNS解決を有効にする", "Prefer H3": "HTTP/3を優先する", "DNS DOH使用HTTP/3": "DNS DOHでHTTP/3プロトコルを使用する", "Respect Rules": "ルートルールに従う", "DNS connections follow routing rules": "DNS接続はルートルールに従います。", "Use Hosts": "Hostsファイルを使用する", "Enable to resolve hosts through hosts file": "Hostsファイルを使用してホスト名を解決する", "Use System Hosts": "システムのHostsファイルを使用する", "Enable to resolve hosts through system hosts file": "システムのHostsファイルを使用してホスト名を解決する", "Direct Nameserver Follow Policy": "直接接続の名前解決サーバーはポリシーに従う", "Whether to follow nameserver policy": "名前解決サーバーのポリシーに従うかどうか", "Default Nameserver": "デフォルトの名前解決サーバー", "Default DNS servers used to resolve DNS servers": "名前解決サーバーを解決するために使用されるデフォルトのDNSサーバー", "Nameserver": "名前解決サーバー", "List of DNS servers": "DNSサーバーのリスト。カンマで区切って指定します。", "Fallback": "フォールバックサーバー", "List of fallback DNS servers": "フォールバックDNSサーバーのリスト。カンマで区切って指定します。", "Proxy Server Nameserver": "プロキシサーバーの名前解決サーバー", "Proxy Node Nameserver": "プロキシノードの名前解決サーバー。プロキシノードのドメイン名を解決するためにのみ使用されます。カンマで区切って指定します。", "Direct Nameserver": "直接接続の名前解決サーバー", "Direct outbound Nameserver": "直接接続の出口名前解決サーバー。systemキーワードをサポートします。カンマで区切って指定します。", "Fake IP Filter": "Fake IPフィルター", "Domains that skip fake IP resolution": "Fake IP解決をスキップするドメイン名。カンマで区切って指定します。", "Nameserver Policy": "名前解決サーバーのポリシー", "Domain-specific DNS server": "特定のドメインのDNSサーバー。複数のサーバーはセミコロンで区切って指定します。形式: domain=server1;server2", "Fallback Filter Settings": "フォールバックフィルター設定", "GeoIP Filtering": "GeoIPフィルタリング", "Enable GeoIP filtering for fallback": "フォールバックのGeoIPフィルタリングを有効にする", "GeoIP Code": "GeoIP国コード", "Fallback IP CIDR": "フォールバックIP CIDR", "IP CIDRs not using fallback servers": "フォールバックサーバーを使用しないIP CIDR。カンマで区切って指定します。", "Fallback Domain": "フォールバックドメイン", "Domains using fallback servers": "フォールバックサーバーを使用するドメイン名。カンマで区切って指定します。", "Hosts Settings": "Hosts設定", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "カスタムのドメイン名からIPまたはドメイン名へのマッピング。カンマで区切って指定します。", "Home Settings": "ホーム設定", "Profile Card": "プロファイルカード", "Current Proxy Card": "現在のプロキシカード", "Network Settings Card": "ネットワーク設定カード", "Proxy Mode Card": "プロキシモードカード", "Traffic Stats Card": "トラフィック統計カード", "Clash Info Cards": "Clash情報カード", "System Info Cards": "システム情報カード", "Website Tests Card": "ウェブサイトテストカード", "Traffic Stats": "トラフィック統計", "Website Tests": "ウェブサイトテスト", "Clash Info": "Clash情報", "Core Version": "コアバージョン", "System Proxy Address": "システムプロキシアドレス", "Uptime": "稼働時間", "Rules Count": "ルール数", "System Info": "システム情報", "OS Info": "オペレーティングシステム情報", "Running Mode": "実行モード", "Sidecar Mode": "ユーザーモード", "Administrator Mode": "管理者モード", "Last Check Update": "最後の更新チェック", "Click to import subscription": "クリックしてサブスクリプションをインポート", "Last Update failed": "前回の更新に失敗しました。", "Next Up": "次回の更新", "No schedule": "予定がありません。", "Unknown": "不明", "Auto update disabled": "自動更新が無効になっています。", "Update subscription successfully": "サブスクリプションの更新に成功しました。", "Update failed, retrying with Clash proxy...": "サブスクリプションの更新に失敗しました。Clashプロキシを使用して再試行します...", "Update with Clash proxy successfully": "Clashプロキシを使用して更新に成功しました。", "Update failed even with Clash proxy": "Clashプロキシを使用しても更新に失敗しました。", "Profile creation failed, retrying with Clash proxy...": "プロファイルの作成に失敗しました。Clashプロキシを使用して再試行します...", "Profile creation succeeded with Clash proxy": "Clashプロキシを使用してプロファイルの作成に成功しました。", "Import failed, retrying with Clash proxy...": "インポートに失敗しました。Clashプロキシを使用して再試行します...", "Profile Imported with Clash proxy": "Clashプロキシを使用してプロファイルのインポートに成功しました。", "Import failed even with Clash proxy": "Clashプロキシを使用してもインポートに失敗しました。", "Current Node": "現在のノード", "No active proxy node": "アクティブなプロキシノードがありません。", "Network Settings": "ネットワーク設定", "Proxy Mode": "プロキシモード", "Group": "プロキシグループ", "Proxy": "ノード", "IP Information Card": "IP情報カード", "IP Information": "IP情報", "Failed to get IP info": "IP情報の取得に失敗しました。", "ISP": "インターネットサービスプロバイダー", "ASN": "自治システム番号", "ORG": "組織", "Location": "位置", "Timezone": "タイムゾーン", "Auto refresh": "自動更新", "Unlock Test": "ロック解除テスト", "Pending": "検査待ち", "Yes": "サポートする", "No": "サポートしない", "Failed": "テストに失敗しました。", "Completed": "検査完了", "Disallowed ISP": "許可されていないインターネットサービスプロバイダー", "Originals Only": "オリジナルのみ", "Unsupported Country/Region": "サポートされていない国/地域", "Controller address copied to clipboard": "API ポートがクリップボードにコピーされました", "Secret copied to clipboard": "API キーがクリップボードにコピーされました", "Copy to clipboard": "クリックしてコピー", "Port Config": "ポート設定", "Configuration saved successfully": "ランダム設定を保存完了", "Enable one-click random API port and key. Click to randomize the port and key": "ワンクリックでランダム API ポートとキーを有効化。ポートとキーをランダム化するにはクリックしてください"}