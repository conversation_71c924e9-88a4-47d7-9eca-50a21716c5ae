{"millis": "milide<PERSON>k", "seconds": "detik", "mins": "menit", "Back": "Kembali", "Close": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON>", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Minimize": "Minimalkan", "Format document": "Format dokumen", "Empty": "Kosong", "New": "<PERSON><PERSON>", "Edit": "Ubah", "Save": "Simpan", "Delete": "Hapus", "Enable": "Aktifkan", "Disable": "Nonaktifkan", "Label-Proxies": "<PERSON>ks<PERSON>", "Label-Profiles": "Profil", "Label-Connections": "Koneks<PERSON>", "Label-Rules": "<PERSON><PERSON><PERSON>", "Label-Logs": "Log", "Label-Test": "<PERSON><PERSON>", "Label-Settings": "<PERSON><PERSON><PERSON><PERSON>", "Dashboard": "<PERSON><PERSON>", "Profile": "Profil", "Help": "Bantuan", "About": "Tentang", "Theme": "<PERSON><PERSON>", "Main Window": "<PERSON><PERSON>", "Group Icon": "Ikon G<PERSON>", "Menu Icon": "<PERSON><PERSON>", "PAC File": "Berkas PAC", "Web UI": "Antarmuka Web", "Hotkeys": "<PERSON><PERSON><PERSON>", "Verge Mixed Port": "Port Campuran Verge", "Verge Socks Port": "Port Socks Verge", "Verge Redir Port": "Port Pengalihan Verge", "Verge Tproxy Port": "Port Tproxy Verge", "Verge Port": "Port Verge", "Verge HTTP Enabled": "HTTP Verge Diaktifkan", "WebDAV URL": "URL WebDAV", "WebDAV Username": "Nama Pengguna WebDAV", "WebDAV Password": "Kata Sandi WebDAV", "Restart App": "<PERSON><PERSON>", "Restart Clash Core": "<PERSON><PERSON> Clash", "TUN Mode": "Mode TUN", "Copy Env": "<PERSON><PERSON>v", "Conf Dir": "Direktori Ko<PERSON>gu<PERSON>i", "Core Dir": "Direktori Core", "Logs Dir": "Direktori Log", "Open Dir": "<PERSON><PERSON>", "More": "<PERSON><PERSON><PERSON>", "Rule Mode": "Mode Aturan", "Global Mode": "Mode Global", "Direct Mode": "<PERSON> Lang<PERSON>g", "Proxies": "<PERSON>ks<PERSON>", "Proxy Groups": "Grup Proksi", "Proxy Provider": "Penyedia <PERSON>i", "Update All": "<PERSON><PERSON><PERSON>", "Update At": "<PERSON><PERSON><PERSON><PERSON>", "rule": "aturan", "global": "global", "direct": "langsung", "script": "skrip", "locate": "<PERSON><PERSON>", "Delay check": "<PERSON><PERSON><PERSON>", "Sort by default": "Urutkan secara default", "Sort by delay": "Urutkan berdasarkan keterlambatan", "Sort by name": "<PERSON><PERSON><PERSON><PERSON> berda<PERSON>kan nama", "Delay check URL": "URL Periksa Keterlambatan", "Delay check to cancel fixed": "Periksa keterlambatan untuk membatalkan tetap", "Proxy basic": "<PERSON><PERSON>", "Proxy detail": "Detail Proksi", "Profiles": "Profil", "Update All Profiles": "<PERSON><PERSON><PERSON>", "View Runtime Config": "<PERSON><PERSON>time", "Reactivate Profiles": "Reaktivasi Profil", "Paste": "Tempel", "Profile URL": "URL Profil", "Import": "Impor", "From": "<PERSON><PERSON>", "Update Time": "<PERSON><PERSON><PERSON>", "Used / Total": "Digunakan / Total", "Expire Time": "<PERSON><PERSON><PERSON>", "Create Profile": "Buat Profil", "Edit Profile": "Ubah Profil", "Edit Proxies": "Ubah Proksi", "Use newlines for multiple uri": "Gunakan baris baru untuk beberapa URI (mendukung pengkodean Base64)", "Edit Rules": "Ubah Aturan", "Rule Type": "<PERSON><PERSON>", "Rule Content": "<PERSON><PERSON><PERSON>", "Proxy Policy": "<PERSON><PERSON>jak<PERSON>", "No Resolve": "Tidak Menyelesaikan", "Prepend Rule": "Tambahkan Aturan di <PERSON>", "Append Rule": "Tambahkan Aturan <PERSON>", "Prepend Group": "Tambahkan Grup di Awal", "Append Group": "Tambahkan Grup di Akhir", "Prepend Proxy": "Tambahkan Proksi di <PERSON>wal", "Append Proxy": "Tambahkan Proksi di <PERSON>", "Rule Condition Required": "<PERSON><PERSON><PERSON>", "Invalid Rule": "Aturan Tidak Valid", "Advanced": "Lanjutan", "Visualization": "Visualisasi", "DOMAIN": "Cocok dengan nama domain lengkap", "DOMAIN-SUFFIX": "Cocok dengan sufiks domain", "DOMAIN-KEYWORD": "Cocok dengan kata kunci domain", "DOMAIN-REGEX": "Cocok dengan domain menggunakan ekspresi reguler", "GEOSITE": "Cocok dengan domain dalam Geosite", "GEOIP": "Cocok dengan kode negara alamat IP", "SRC-GEOIP": "Cocok dengan kode negara alamat IP sumber", "IP-ASN": "Cocok dengan ASN alamat IP", "SRC-IP-ASN": "Cocok dengan ASN alamat IP sumber", "IP-CIDR": "Cocok dengan rentang alamat IP", "IP-CIDR6": "Cocok dengan rentang alamat IPv6", "SRC-IP-CIDR": "Cocok dengan rentang alamat IP sumber", "IP-SUFFIX": "Cocok dengan rentang sufiks alamat IP", "SRC-IP-SUFFIX": "Cocok dengan rentang sufiks alamat IP sumber", "SRC-PORT": "Cocok dengan rentang port sumber", "DST-PORT": "Cocok dengan rentang port tujuan", "IN-PORT": "Cocok dengan port masuk", "DSCP": "<PERSON><PERSON><PERSON> (hanya untuk tproxy UDP masuk)", "PROCESS-NAME": "Cocok dengan nama proses (nama paket Android)", "PROCESS-PATH": "Cocok dengan jalur proses leng<PERSON>p", "PROCESS-NAME-REGEX": "Cocok dengan nama proses lengkap menggunakan ekspresi reguler (nama paket Android)", "PROCESS-PATH-REGEX": "Cocok dengan jalur proses lengkap menggunakan ekspresi reguler", "NETWORK": "Cocok dengan protokol transportasi (tcp/udp)", "UID": "Cocok dengan ID PENGGUNA Linux", "IN-TYPE": "Cocok dengan jenis masuk", "IN-USER": "Cocok dengan nama pengguna masuk", "IN-NAME": "Cocok dengan nama masuk", "SUB-RULE": "Sub-aturan", "RULE-SET": "<PERSON><PERSON><PERSON> set aturan", "AND": "Logika DAN", "OR": "Logika ATAU", "NOT": "Logika TIDAK", "MATCH": "Cocok dengan semua permintaan", "DIRECT": "Data langsung keluar", "REJECT": "<PERSON><PERSON><PERSON> permintaan", "REJECT-DROP": "<PERSON><PERSON><PERSON> per<PERSON>", "PASS": "<PERSON><PERSON> aturan ini saat cocok", "Edit Groups": "Ubah Grup Proksi", "Group Type": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON> proksi secara manual", "url-test": "Pilih proksi berdasarkan keterlambatan tes URL", "fallback": "<PERSON><PERSON><PERSON> ke proksi lain saat terjadi kesalahan", "load-balance": "Distribusikan proksi berdasarkan penyeimbangan beban", "relay": "Lewatkan melalui rantai proksi yang ditentukan", "Group Name": "<PERSON><PERSON>", "Use Proxies": "<PERSON><PERSON><PERSON>", "Use Provider": "<PERSON><PERSON><PERSON>", "Health Check Url": "URL Pemeriksaan <PERSON>", "Expected Status": "Status yang <PERSON>", "Interval": "Interval", "Lazy": "Malas", "Timeout": "<PERSON><PERSON><PERSON>", "Max Failed Times": "<PERSON><PERSON><PERSON>", "Interface Name": "<PERSON><PERSON>", "Routing Mark": "Tanda Routing", "Include All": "Ser<PERSON><PERSON> dan <PERSON>", "Include All Providers": "Ser<PERSON><PERSON>", "Include All Proxies": "Sertakan <PERSON>", "Exclude Filter": "Kecualikan Filter", "Exclude Type": "<PERSON><PERSON><PERSON><PERSON>", "Disable UDP": "Nonaktifkan UDP", "Hidden": "Tersemb<PERSON><PERSON>", "Group Name Required": "Nama Grup Diperlukan", "Group Name Already Exists": "Nama Grup Sudah Ada", "Extend Config": "<PERSON><PERSON><PERSON>", "Extend Script": "<PERSON><PERSON><PERSON>", "Global Merge": "<PERSON><PERSON><PERSON>", "Global Script": "<PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON>", "Name": "<PERSON><PERSON>", "Descriptions": "<PERSON><PERSON><PERSON><PERSON>", "Subscription URL": "URL Langganan", "Update Interval": "Interval Pembaruan", "Choose File": "<PERSON><PERSON><PERSON>", "Use System Proxy": "<PERSON><PERSON><PERSON>", "Use Clash Proxy": "<PERSON><PERSON><PERSON>", "Accept Invalid Certs (Danger)": "<PERSON><PERSON> Serti<PERSON> Tidak Valid (Bahaya)", "Refresh": "Segarkan", "Home": "Be<PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON>", "Edit Info": "Ubah Info", "Edit File": "Ubah Berkas", "Open File": "<PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON>", "Update(Proxy)": "<PERSON><PERSON><PERSON> (Proksi)", "Confirm deletion": "<PERSON>n<PERSON><PERSON><PERSON>", "This operation is not reversible": "Operasi ini tidak dapat di<PERSON>an", "Script Console": "<PERSON><PERSON><PERSON>", "To Top": "<PERSON>", "To End": "<PERSON>", "Connections": "Koneks<PERSON>", "Table View": "<PERSON><PERSON><PERSON>", "List View": "<PERSON><PERSON><PERSON>", "Close All": "<PERSON><PERSON><PERSON>", "Default": "<PERSON><PERSON><PERSON>", "Download Speed": "Kecepatan Unduh", "Upload Speed": "Kecepatan Unggah", "Host": "Host", "Downloaded": "<PERSON><PERSON><PERSON>", "Uploaded": "Diunggah", "DL Speed": "Kecepatan Unduh", "UL Speed": "Kecepatan Unggah", "Chains": "<PERSON><PERSON><PERSON>", "Rule": "<PERSON><PERSON><PERSON>", "Process": "Proses", "Time": "<PERSON><PERSON><PERSON>", "Source": "Sumber", "Destination": "IP Tujuan", "DestinationPort": "Port Tujuan", "Close Connection": "<PERSON><PERSON><PERSON>", "Rules": "<PERSON><PERSON><PERSON>", "Rule Provider": "<PERSON><PERSON><PERSON>", "Logs": "Log", "Pause": "<PERSON><PERSON>", "Resume": "<PERSON><PERSON><PERSON><PERSON>", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "Test": "<PERSON><PERSON>", "Test All": "<PERSON><PERSON>", "Create Test": "B<PERSON>t Te<PERSON>", "Edit Test": "Ubah Tes", "Icon": "<PERSON><PERSON>", "Test URL": "URL Tes", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "System Setting": "Pengaturan Sistem", "Tun Mode": "Mode Tun (NIC Virtual)", "Reset to Default": "<PERSON><PERSON> Ulang ke Default", "Tun Mode Info": "Mode Tun (NIC Virtual): Menangkap semua lalu lintas sistem, saat diaktif<PERSON>, tidak perlu mengaktifkan proksi sistem.", "Stack": "<PERSON>mp<PERSON><PERSON>", "System and Mixed Can Only be Used in Service Mode": "Sistem dan <PERSON><PERSON>n dalam Mode Layanan", "Device": "<PERSON><PERSON>", "Auto Route": "<PERSON><PERSON>", "Strict Route": "<PERSON><PERSON>", "Auto Detect Interface": "Deteksi <PERSON>", "DNS Hijack": "Pembajakan DNS", "MTU": "Unit Transmisi Maksimum", "Service Mode": "<PERSON>", "Service Mode Info": "Harap instal mode layanan sebelum mengaktifkan mode TUN. Proses kernel yang dimulai oleh layanan dapat memperoleh izin untuk menginstal kartu jaringan virtual (mode TUN)", "Current State": "Status Saat Ini", "pending": "tertunda", "installed": "terinstal", "uninstall": "dicopot", "active": "aktif", "unknown": "tidak <PERSON>i", "Information: Please make sure that the Clash Verge Service is installed and enabled": "Informasi: <PERSON><PERSON>n bahwa <PERSON>n Clash Verge terinstal dan diaktifkan", "Install": "Instal", "Uninstall": "Copot", "Disable Service Mode": "Nonaktifkan Mode Layanan", "System Proxy": "<PERSON><PERSON><PERSON>", "System Proxy Info": "Aktifkan untuk mengubah pengaturan proksi sistem operasi. <PERSON><PERSON> pengaktifan gagal, ubah pengaturan proksi sistem operasi secara manual", "System Proxy Setting": "Pengaturan Proksi Sistem", "Current System Proxy": "Proksi Sistem Saat Ini", "Enable status": "Status Pengaktifan:", "Enabled": "Diaktifkan", "Disabled": "Dinonaktifkan", "Server Addr": "Alamat Server: ", "Proxy Host": "Host <PERSON>ksi", "Invalid Proxy Host Format": "Format Host Proksi Tidak Valid", "Not available": "Tidak tersedia", "Proxy Guard": "Penjaga Proksi", "Proxy Guard Info": "Aktifkan untuk mencegah perangkat lunak lain mengubah pengaturan proksi sistem operasi", "Guard Duration": "<PERSON><PERSON><PERSON>", "Always use Default Bypass": "<PERSON><PERSON><PERSON> Bypass Default", "Proxy Bypass": "Pengaturan Bypass Proksi: ", "Bypass": "Bypass: ", "Use PAC Mode": "Gunakan Mode PAC", "PAC Script Content": "Konten Skrip PAC", "PAC URL": "URL PAC: ", "Auto Launch": "<PERSON><PERSON>un<PERSON><PERSON>", "Silent Start": "<PERSON><PERSON>", "Silent Start Info": "Mulai program dalam mode latar belakang tanpa menampilkan panel", "TG Channel": "Saluran Telegram", "Manual": "Manual", "Github Repo": "<PERSON><PERSON><PERSON><PERSON>", "Clash Setting": "Pengaturan Clash", "Allow Lan": "Izinkan LAN", "Network Interface": "<PERSON><PERSON><PERSON><PERSON>", "Ip Address": "Alamat IP", "Mac Address": "Alamat MAC", "IPv6": "IPv6", "Unified Delay": "Keterlambatan Terpadu", "Unified Delay Info": "Saat keterlambatan terpadu diak<PERSON>, dua tes keterlambatan akan dilakukan untuk menghilangkan perbedaan keterlambatan antara berbagai jenis node yang disebabkan oleh jabat tangan koneksi, dll.", "Log Level": "Tingkat Log", "Log Level Info": "Ini hanya berlaku untuk file log kernel di folder layanan di direktori log.", "Port Config": "Konfigurasi Port", "Random Port": "Port Acak", "Mixed Port": "Port Campuran", "Socks Port": "Port Socks", "Http Port": "Port Http(s)", "Redir Port": "Port Redir", "Tproxy Port": "Port Tproxy", "External": "Eksternal", "External Controller": "<PERSON><PERSON><PERSON> Eksternal", "Core Secret": "<PERSON><PERSON><PERSON>", "Recommended": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open URL": "Buka URL", "Replace host, port, secret with %host, %port, %secret": "Ganti host, port, rahasia dengan %host, %port, %secret", "Support %host, %port, %secret": "Dukung %host, %port, %secret", "Clash Core": "<PERSON><PERSON>", "Upgrade": "Tingkatkan", "Restart": "<PERSON><PERSON>", "Release Version": "<PERSON><PERSON><PERSON>", "Alpha Version": "Versi Alpha", "Please Enable Service Mode": "Harap Instal dan Aktifkan Mode Layanan Terlebih Dahulu", "Please enter your root password": "<PERSON><PERSON> masukkan kata sandi root Anda", "Grant": "Izinkan", "Open UWP tool": "Buka alat UWP", "Open UWP tool Info": "Sejak Windows 8, aplikasi UWP (seperti Microsoft Store) dibatasi dari mengakses layanan jaringan host lokal secara langsung, dan alat ini dapat digunakan untuk melewati pembatasan ini", "Update GeoData": "<PERSON><PERSON><PERSON> GeoData", "Verge Setting": "Pengaturan Verge", "Language": "Bahasa", "Theme Mode": "Mode Tema", "theme.light": "Terang", "theme.dark": "<PERSON><PERSON><PERSON>", "theme.system": "Sistem", "Tray Click Event": "<PERSON><PERSON><PERSON>", "Show Main Window": "<PERSON><PERSON><PERSON><PERSON>", "Copy Env Type": "<PERSON><PERSON>", "Copy Success": "<PERSON><PERSON>", "Start Page": "<PERSON><PERSON>", "Startup Script": "<PERSON><PERSON><PERSON>up", "Browse": "<PERSON><PERSON><PERSON><PERSON>", "Theme Setting": "<PERSON><PERSON><PERSON><PERSON>", "Primary Color": "<PERSON><PERSON>", "Secondary Color": "<PERSON><PERSON>", "Primary Text": "<PERSON><PERSON>", "Secondary Text": "<PERSON><PERSON>", "Info Color": "Warna Info", "Warning Color": "<PERSON><PERSON>", "Error Color": "<PERSON><PERSON>", "Success Color": "<PERSON><PERSON>", "Font Family": "<PERSON><PERSON><PERSON><PERSON>", "CSS Injection": "Injeksi CSS", "Layout Setting": "Pengat<PERSON><PERSON>", "Traffic Graph": "<PERSON><PERSON>", "Memory Usage": "<PERSON><PERSON><PERSON><PERSON>", "Memory Cleanup": "Ketuk untuk membersihkan memori", "Proxy Group Icon": "Ikon Grup Proksi", "Nav Icon": "<PERSON><PERSON>", "Monochrome": "Monokrom", "Colorful": "<PERSON><PERSON><PERSON><PERSON>", "Tray Icon": "<PERSON><PERSON>", "Common Tray Icon": "<PERSON><PERSON>", "System Proxy Tray Icon": "Ikon Tray Proksi Sistem", "Tun Tray Icon": "<PERSON><PERSON>", "Miscellaneous": "Lain-lain", "App Log Level": "Tingkat Log Aplikasi", "Auto Close Connections": "<PERSON><PERSON><PERSON>", "Auto Close Connections Info": "Hentikan koneksi yang sudah ada saat pemilihan grup proksi atau mode proksi berubah", "Auto Check Update": "<PERSON>iksa <PERSON>", "Enable Builtin Enhanced": "Aktifkan Peningkatan Bawaan", "Enable Builtin Enhanced Info": "Penanganan kompatibilitas untuk file konfigurasi", "Proxy Layout Columns": "<PERSON><PERSON><PERSON> Tata Letak Proksi", "Auto Columns": "<PERSON><PERSON><PERSON>", "Auto Log Clean": "Pembersihan <PERSON> Otomatis", "Never Clean": "<PERSON><PERSON>", "Retain _n Days": "Simpan {{n}} Hari", "Default Latency Test": "<PERSON><PERSON>", "Default Latency Test Info": "Digunakan hanya untuk pengujian permintaan klien HTTP dan tidak akan mempengaruhi file konfigurasi", "Default Latency Timeout": "<PERSON><PERSON><PERSON>", "Hotkey Setting": "<PERSON><PERSON><PERSON><PERSON>", "Enable Global Hotkey": "Aktifkan Hotkey Global", "open_or_close_dashboard": "Buka/Tutup <PERSON>", "clash_mode_rule": "Mode Aturan", "clash_mode_global": "Mode Global", "clash_mode_direct": "<PERSON> Lang<PERSON>g", "toggle_system_proxy": "Aktifkan/Nonaktifkan Proksi Sistem", "toggle_tun_mode": "Aktifkan/Nonaktifkan Mode Tun", "Backup Setting": "Pengaturan Cadangan", "Backup Setting Info": "Mendukung file konfigurasi cadangan WebDAV", "Runtime Config": "Konfigurasi Runtime", "Open Conf Dir": "<PERSON>uka Direktori Ko<PERSON>", "Open Conf Dir Info": "Jika perangkat lunak berjalan tidak normal, CADANGKAN dan hapus semua file di folder ini lalu mulai ulang perangkat lunak", "Open Core Dir": "Buka Direktori Core", "Open Logs Dir": "Buka Direktori Log", "Check for Updates": "<PERSON>ik<PERSON>", "Go to Release Page": "<PERSON>gi ke Halaman Rilis", "Portable Updater Error": "Versi portabel tidak mendukung pembaruan dalam aplikasi. Harap unduh dan ganti secara manual", "Break Change Update Error": "Versi ini adalah pembaruan besar dan tidak mendukung pembaruan dalam aplikasi. Hara<PERSON> hapus instalasi dan unduh serta instal versi baru secara manual", "Open Dev Tools": "Buka Alat Pengembang", "Exit": "<PERSON><PERSON><PERSON>", "Verge Version": "Versi Verge", "ReadOnly": "Hanya Baca", "ReadOnlyMessage": "Tidak dapat mengedit di editor hanya baca", "Filter": "Filter", "Filter conditions": "<PERSON><PERSON><PERSON>", "Match Case": "Cocokkan Kasus", "Match Whole Word": "Cocokkan Kata Utuh", "Use Regular Expression": "Gunakan Ekspresi Reguler", "Profile Imported Successfully": "Profil <PERSON>", "Profile Switched": "<PERSON><PERSON>", "Profile Reactivated": "<PERSON><PERSON>", "Only YAML Files Supported": "Hanya File YAML yang <PERSON>", "Settings Applied": "Pengaturan Diterapkan", "Service Installed Successfully": "<PERSON><PERSON><PERSON>", "Service Uninstalled Successfully": "<PERSON><PERSON><PERSON>", "Proxy Daemon Duration Cannot be Less than 1 Second": "Durasi Daemon Proksi Tidak Boleh Kurang dari 1 Detik", "Invalid Bypass Format": "Format Bypass Tidak Valid", "Clash Port Modified": "Port Clash Diubah", "Port Conflict": "Konflik Port", "Restart Application to Apply Modifications": "<PERSON><PERSON> Aplikasi untuk Menerapkan Modifikasi", "External Controller Address Modified": "<PERSON><PERSON><PERSON> Eksternal Diubah", "Permissions Granted Successfully for _clash Core": "<PERSON>zin Berhasil Diberikan untuk Core {{core}}", "Core Version Updated": "Versi <PERSON>", "Clash Core Restarted": "Core Clash <PERSON><PERSON><PERSON>", "GeoData Updated": "GeoData <PERSON>", "Currently on the Latest Version": "Saat ini pada Versi Terbaru", "Import Subscription Successful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WebDAV Server URL": "URL Server WebDAV", "Username": "<PERSON><PERSON>", "Password": "<PERSON><PERSON>", "Backup": "Cadangan", "Filename": "<PERSON><PERSON>", "Actions": "<PERSON><PERSON><PERSON>", "Restore": "Pulihkan", "No Backups": "Tidak ada cadangan yang tersedia", "WebDAV URL Required": "URL WebDAV tidak boleh kosong", "Invalid WebDAV URL": "Format URL WebDAV tidak valid", "Username Required": "<PERSON>a pengguna tidak boleh kosong", "Password Required": "Kata sandi tidak boleh kosong", "Failed to Fetch Backups": "Gagal mengambil file cadangan", "WebDAV Config Saved": "Konfigurasi WebDAV berhasil disimpan", "WebDAV Config Save Failed": "Gagal menyimpan konfigurasi WebDAV: {{error}}", "Backup Created": "Cadangan berhasil dibuat", "Backup Failed": "Cadangan gagal: {{error}}", "Delete Backup": "<PERSON><PERSON>", "Restore Backup": "Pulihkan Cadangan", "Backup Time": "<PERSON><PERSON><PERSON>", "Confirm to delete this backup file?": "Konfirmasi untuk menghapus file cadangan ini?", "Confirm to restore this backup file?": "Konfirmasi untuk memulihkan file cadangan ini?", "Restore Success, App will restart in 1s": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> akan dimulai ulang dalam 1 detik", "Failed to fetch backup files": "Gagal mengambil file cadangan", "Enable Tray Speed": "Aktifkan Tray Speed", "LightWeight Mode": "<PERSON>", "LightWeight Mode Info": "Tutup GUI dan biarkan hanya kernel yang berjalan", "Config Validation Failed": "Validasi konfigurasi lang<PERSON>an gagal, periksa file konfigurasi, per<PERSON><PERSON>, detail k<PERSON><PERSON>han:", "Boot Config Validation Failed": "Validasi konfigurasi saat boot gagal, menggunakan konfigurasi default, periksa file konfigurasi, detail kesalahan:", "Core Change Config Validation Failed": "Validasi konfigurasi saat ganti inti gagal, menggunakan konfigurasi default, periksa file konfigurasi, detail kesalahan:", "Config Validation Process Terminated": "Proses validasi dihentikan", "Script Syntax Error": "Kesalahan sintaks skrip, <PERSON><PERSON><PERSON>", "Script Missing Main": "<PERSON><PERSON><PERSON> skrip, <PERSON><PERSON><PERSON>", "File Not Found": "File tidak di<PERSON>, per<PERSON><PERSON> di<PERSON>", "Script File Error": "Kesalahan file skrip, per<PERSON><PERSON> di<PERSON>an", "Core Changed Successfully": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Failed to Change Core": "Gagal mengubah inti", "Verge Basic Setting": "<PERSON><PERSON><PERSON><PERSON>", "Verge Advanced Setting": "Pengaturan Lanjutan Verge", "TUN requires Service Mode": "Mode TUN memerlukan layanan", "Install Service": "Instal Layanan", "Installing Service...": "<PERSON><PERSON><PERSON>...", "Service Administrator Prompt": "Clash Verge memerlukan hak administrator untuk menginstal ulang layanan sistem"}