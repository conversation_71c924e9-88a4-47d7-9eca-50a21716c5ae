{"millis": "миллисекунды", "seconds": "секунды", "mins": "минуты", "Back": "Назад", "Close": "Закрыть", "Cancel": "Отмена", "Confirm": "Подтвердить", "Maximize": "Развернуть", "Minimize": "Свернуть", "Format document": "Форматировать документ", "Empty": "Пусто", "New": "Новый", "Edit": "Редактировать", "Save": "Сохранить", "Delete": "Удалить", "Enable": "Включить", "Disable": "Отключить", "Label-Home": "Главная", "Label-Proxies": "Прокси", "Label-Profiles": "Профили", "Label-Connections": "Соединения", "Label-Rules": "Правила", "Label-Logs": "Логи", "Label-Unlock": "Тест", "Label-Settings": "Настройки", "Proxies": "Прокси", "Proxy Groups": "Группы прокси", "Proxy Provider": "Провайдер прокси", "Update All": "Обновить все", "Update At": "Обновлено в", "rule": "правила", "global": "глобальный", "direct": "прямой", "script": "скриптовый", "locate": "Местоположение", "Delay check": "Проверка задержки", "Sort by default": "Сортировать по умолчанию", "Sort by delay": "Сортировать по задержке", "Sort by name": "Сортировать по названию", "Delay check URL": "URL проверки задержки", "Delay check to cancel fixed": "Проверка задержки для отмены фиксированного", "Proxy basic": "Отображать меньше сведений о прокси", "Proxy detail": "Отображать больше сведений о прокси", "Profiles": "Профили", "Update All Profiles": "Обновить все профили", "View Runtime Config": "Просмотреть используемый конфиг", "Reactivate Profiles": "Перезапустить профиль", "Paste": "Вставить", "Profile URL": "URL профиля", "Import": "Импорт", "From": "От", "Update Time": "Время обновления", "Used / Total": "Использовано / Всего", "Expire Time": "Время окончания", "Create Profile": "Создать профиль", "Edit Profile": "Изменить профиль", "Edit Proxies": "Редактировать прокси", "Use newlines for multiple uri": "Используйте символы новой строки для нескольких URI (поддерживается кодировка Base64)", "Edit Rules": "Редактировать правила", "Rule Type": "Тип правила", "Rule Content": "Содержимое правила", "Proxy Policy": "Политика прокси", "No Resolve": "Без разрешения", "Prepend Rule": "Добавить правило в начало", "Append Rule": "Добавить правило в конец", "Prepend Group": "Добавить группу в начало", "Append Group": "Добавить группу в конец", "Prepend Proxy": "Добавить прокси в начало", "Append Proxy": "Добавить прокси в конец", "Rule Condition Required": "Требуется условие правила", "Invalid Rule": "Недействительное правило", "Advanced": "Дополнительно", "Visualization": "Визуализация", "DOMAIN": "Соответствует полному доменному имени", "DOMAIN-SUFFIX": "Соответствует суффиксу домена", "DOMAIN-KEYWORD": "Соответствует ключевому слову домена", "DOMAIN-REGEX": "Соответствует домену с использованием регулярных выражений", "GEOSITE": "Соответствует доменам в Geosite", "GEOIP": "Соответствует коду страны IP-адреса", "SRC-GEOIP": "Соответствует коду страны исходного IP-адреса", "IP-ASN": "Соответствует ASN IP-адреса", "SRC-IP-ASN": "Соответствует ASN исходного IP-адреса", "IP-CIDR": "Соответствует диапазону IP-адресов", "IP-CIDR6": "Соответствует диапазону IPv6-ад<PERSON><PERSON><PERSON>ов", "SRC-IP-CIDR": "Соответствует диапазону исходных IP-адресов", "IP-SUFFIX": "Соответствует диапазону суффиксов IP-адресов", "SRC-IP-SUFFIX": "Соответствует диапазону суффиксов исходных IP-адресов", "SRC-PORT": "Соответствует диапазону исходных портов", "DST-PORT": "Соответствует диапазону целевых портов", "IN-PORT": "Соответствует входящему порту", "DSCP": "Маркировка DSCP (только для tproxy UDP входящего)", "PROCESS-NAME": "Соответствует имени процесса (имя пакета Android)", "PROCESS-PATH": "Соответствует полному пути процесса", "PROCESS-NAME-REGEX": "Соответствует имени процесса с использованием регулярных выражений (имя пакета Android)", "PROCESS-PATH-REGEX": "Соответствует полному пути процесса с использованием регулярных выражений", "NETWORK": "Соответствует транспортному протоколу (tcp/udp)", "UID": "Соответствует USER ID в Linux", "IN-TYPE": "Соответствует типу входящего соединения", "IN-USER": "Соответствует имени пользователя входящего соединения", "IN-NAME": "Соответствует имени входящего соединения", "SUB-RULE": "Подправило", "RULE-SET": "Соответствует набору правил", "AND": "Логическое И", "OR": "Логическое ИЛИ", "NOT": "Логическое НЕ", "MATCH": "Соответствует всем запросам", "DIRECT": "Данные направляются напрямую наружу", "REJECT": "Перехватывает запросы", "REJECT-DROP": "Отклоняет запросы", "PASS": "Пропускает это правило при совпадении", "Edit Groups": "Редактировать группы прокси", "Group Type": "Тип группы", "select": "Выбор прокси вручную", "url-test": "Выбор прокси на основе задержки теста URL", "fallback": "Переключение на другой прокси при ошибке", "load-balance": "Распределение прокси на основе балансировки нагрузки", "relay": "Передача через определенную цепочку прокси", "Group Name": "Имя группы", "Use Proxies": "Использовать прокси", "Use Provider": "Использовать провайдера", "Health Check Url": "URL проверки здоровья", "Expected Status": "Ожид<PERSON><PERSON><PERSON>ый статус", "Interval": "Интервал", "Lazy": "Ленивый", "Timeout": "Таймаут", "Max Failed Times": "Максимальное количество неудач", "Interface Name": "Имя интерфейса", "Routing Mark": "Марка маршрутизации", "Include All": "Включить все прокси и провайдеры", "Include All Providers": "Включить всех провайдеров", "Include All Proxies": "Включить все прокси", "Exclude Filter": "Исключить фильтр", "Exclude Type": "Тип исключения", "Disable UDP": "Отключить UDP", "Hidden": "Скрытый", "Group Name Required": "Требуется имя группы", "Group Name Already Exists": "Имя группы уже существует", "Extend Config": "Измени<PERSON><PERSON> Merge", "Extend Script": "Изме<PERSON><PERSON><PERSON><PERSON>", "Global Merge": "Глобальный расширенный Настройки", "Global Script": "Глобальный расширенный скрипт", "Type": "Тип", "Name": "Название", "Descriptions": "Описание", "Subscription URL": "URL подписки", "Update Interval": "Интервал обновления", "Choose File": "Выбрать файл", "Use System Proxy": "Использовать системный прокси для обновления", "Use Clash Proxy": "Использовать прокси Clash для обновления", "Accept Invalid Certs (Danger)": "Принимать недействительные сертификаты (ОПАСНО)", "Refresh": "Обновить", "Home": "Главная", "Select": "Выбрать", "Edit Info": "Изменить информацию", "Edit File": "Изменить файл", "Open File": "Открыть файл", "Update": "Обновить", "Update(Proxy)": "Обновить (прокси)", "Confirm deletion": "Подтвердите удаление", "This operation is not reversible": "Эта операция необратима", "Script Console": "Консоль скрипта", "To Top": "Наверх", "To End": "<PERSON><PERSON><PERSON><PERSON>", "Connections": "Соединения", "Table View": "Отображать в виде таблицы", "List View": "Отображать в виде списка", "Close All": "Закрыть всё", "Upload": "Загрузка", "Download": "Скачивание", "Download Speed": "Скорость скачивания", "Upload Speed": "Скорость загрузки", "Host": "Хо<PERSON>т", "Downloaded": "Скачано", "Uploaded": "Загружено", "DL Speed": "Скорость скачивания", "UL Speed": "Скорость загрузки", "Active Connections": "Активные соединения", "Chains": "Цепочки", "Rule": "Правило", "Process": "Процесс", "Time": "Время подключения", "Source": "Исходный адрес", "Destination": "IP-адрес назначения", "DestinationPort": "Целевой порт", "Close Connection": "Закрыть соединение", "Rules": "Правила", "Rule Provider": "Провайдеры правил", "Logs": "Логи", "Pause": "Пауза", "Resume": "Возобновить", "Clear": "Очистить", "Test": "Тест", "Test All": "Тестировать все", "Testing...": "Тестирование ...", "Create Test": "Создать тест", "Edit Test": "Редактировать тест", "Icon": "Иконка", "Test URL": "URL проверка", "Settings": "Настройки", "System Setting": "Настройки системы", "Tun Mode": "Режим TUN", "TUN requires Service Mode": "Режим TUN требует установленную службу Clash Verge", "Install Service": "Установить службу", "Reset to Default": "Сбросить настройки", "Tun Mode Info": "Режим Tun: захватывает весь системный трафик, при включении нет необходимости включать системный прокси-сервер.", "System Proxy Enabled": "Системный прокси включен, ваши приложения будут получать доступ к сети через него", "System Proxy Disabled": "Системный прокси отключен, большинству пользователей рекомендуется включить эту опцию", "TUN Mode Enabled": "Режим TUN включен, приложения будут получать доступ к сети через виртуальную сетевую карту", "TUN Mode Disabled": "Режим TUN отключен", "TUN Mode Service Required": "Режим TUN требует установленную службу Clash Verge", "TUN Mode Intercept Info": "Режим TUN может перехватить трафик всех приложений, подходит для приложений, которые не работают в режиме системного прокси.", "Rule Mode Description": "Направляет трафик в соответствии с предустановленными правилами", "Global Mode Description": "Направляет весь трафик через прокси-серверы", "Direct Mode Description": "Весь трафик обходит прокси, но передается ядром Clash для целевых серверов, подходит для конкретных сценариев, требующих распределения трафика ядра", "Stack": "Стек", "System and Mixed Can Only be Used in Service Mode": "Стэк System и Mixed могут использоваться только в режиме системной службы", "Device": "Имя устройства", "Auto Route": "Автоматическая маршрутизация", "Strict Route": "Строгая маршрутизация", "Auto Detect Interface": "Автоопределение интерфейса", "DNS Hijack": "DNS-перехват", "MTU": "MTU", "Service Mode": "Режим системной службы", "Service Mode Info": "Установите режим системной службы перед включением режима TUN. Процесс ядра, запущенный службой, может получить разрешение на установку виртуальной сетевой карты (режим TUN).", "Current State": "Текущее состояние", "pending": "Ожидающий", "installed": "Установленный", "uninstall": "Не установленный", "active": "Активированный", "unknown": "неизвестный", "Information: Please make sure that the Clash Verge Service is installed and enabled": "Информация: Пож<PERSON><PERSON><PERSON>йста, убедитесь, что сервис Clash Verge Service установлен и включен", "Install": "Установить", "Uninstall": "Удалить", "Disable Service Mode": "Отключить режим системной службы", "System Proxy": "Системный прокси", "System Proxy Info": "Разрешить изменение настроек прокси-сервера операционной системы. Если разрешение не удастся, измените настройки прокси-сервера операционной системы вручную", "System Proxy Setting": "Настройка системного прокси", "Current System Proxy": "Текущий системный прокси", "Enable status": "Статус включения", "Enabled": "Включено", "Disabled": "Отключено", "Server Addr": "Адрес сервера: ", "Proxy Host": "Хост прокси", "Invalid Proxy Host Format": "Неверный формат хоста прокси", "Not available": "Недоступно", "Proxy Guard": "Proxy Guard", "Proxy Guard Info": "Включите эту функцию чтобы предотвратить изменение настроек прокси-сервера операционной системы другим ПО", "Guard Duration": "Период защиты", "Always use Default Bypass": "Всегда использовать стандартное обходное решение", "Use Bypass Check": "Используйте проверку обхода", "Proxy Bypass": "Игнорируемые адреса: ", "Bypass": "Игнорируемые адреса: ", "Use PAC Mode": "Используйте режим PAC", "PAC Script Content": "Содержание сценария PAC", "PAC URL": "Адрес PAC: ", "Auto Launch": "Автозапуск", "Silent Start": "<PERSON>и<PERSON><PERSON> запуск", "Silent Start Info": "Запускать программу в фоновом режиме без отображения панели", "TG Channel": "Telegram-канал", "Manual": "Документация", "Github Repo": "GitHub репозиторий", "Clash Setting": "Настройки Clash", "Allow Lan": "Разрешить доступ из локальной сети", "Network Interface": "Сетевой интерфейс", "Ip Address": "IP адрес", "Mac Address": "MAC адрес", "IPv6": "IPv6", "Unified Delay": "Точная задержка", "Unified Delay Info": "Когда унифицированная(точная) задержка включена, будут выполнены два теста задержки, чтобы устранить различия в задержке между разными типами узлов, вызванные подтверждением соединения и т. д", "Log Level": "Уровень логов", "Log Level Info": "Это действует только на файлы журнала ядра в служебном файле в каталоге журналов.", "Port Config": "Настройка порта", "Random Port": "Случайный порт", "Mixed Port": "Смешанный прокси-порт", "Socks Port": "Порт <PERSON>-прокси", "Http Port": "Порт Http(s)-прокси", "Redir Port": "Порт прозрачного прокси Redir", "Tproxy Port": "Порт прозрачного прокси Tproxy", "External": "Внешний контроллер", "External Controller": "Адрес прослушивания внешнего контроллера", "Core Secret": "Секрет", "Recommended": "Рекомендуется", "Open URL": "Перейти по адресу", "Replace host, port, secret with %host, %port, %secret": "Замените хост, порт и секрет на %host, %port, %secret", "Support %host, %port, %secret": "Поддерживаются %host, %port, %secret", "Clash Core": "Я<PERSON><PERSON><PERSON> Clash", "Upgrade": "Обновить", "Restart": "Перезапустить", "Release Version": "Официальная версия", "Alpha Version": "Альфа-версия", "Please Enable Service Mode": "Пожалуйста, сначала установите и включите режим системной службы", "Please enter your root password": "Пожалуйста, введите ваш пароль root", "Grant": "Предоставить", "Open UWP tool": "Открыть UWP инструмент", "Open UWP tool Info": "С Windows 8 приложения UWP (такие как Microsoft Store) ограничены в прямом доступе к сетевым службам локального хоста, и этот инструмент позволяет обойти это ограничение", "Update GeoData": "Обновить GeoData", "Verge Basic Setting": "Основные настройки Verge", "Verge Advanced Setting": "Расширенные настройки Verge", "Language": "Язык", "Theme Mode": "Цветовая тема", "theme.light": "Светлая", "theme.dark": "Тёмная", "theme.system": "Системная", "Tray Click Event": "Событие при щелчке по иконке в трее", "Show Main Window": "Показать главное окно", "Show Tray Menu": "Показать меню в трее", "Copy Env Type": "Скопировать тип Env", "Copy Success": "Скопировано", "Start Page": "Главная страница", "Startup Script": "Скрипт запуска", "Browse": "Просмотреть", "Theme Setting": "Настройки темы", "Primary Color": "Основной цвет", "Secondary Color": "Вторичный цвет", "Primary Text": "Первичный текст", "Secondary Text": "Вторичный текст", "Info Color": "Информационный цвет", "Warning Color": "Цвет предупреждения", "Error Color": "Цвет ошибки", "Success Color": "Цвет успеха", "Font Family": "Семейство шрифтов", "CSS Injection": "Внедрение CSS", "Layout Setting": "Настройки раскладки", "Traffic Graph": "График трафика", "Memory Usage": "Использование памяти", "Memory Cleanup": "Нажмите, чтобы очистить память", "Proxy Group Icon": "Иконка Группы прокси", "Nav Icon": "Иконки навигации", "Monochrome": "Монохромные", "Colorful": "Цветные", "Tray Icon": "Иконка в трее", "Common Tray Icon": "Общий значок в трее", "System Proxy Tray Icon": "Значок системного прокси в трее", "Tun Tray Icon": "Значок TUN в трее", "Miscellaneous": "Расширенные настройки", "App Log Level": "Уровень журнала приложения", "Auto Close Connections": "Автоматическое закрытие соединений", "Auto Close Connections Info": "Закрыть установленные соединения при изменении выбора группы прокси или режима прокси", "Auto Check Update": "Автоматическая проверка обновлений", "Enable Builtin Enhanced": "Включить встроенные улучшения", "Enable Builtin Enhanced Info": "Обработка совместимости для файла конфигурации", "Proxy Layout Columns": "Количество столбцов в макете прокси", "Auto Columns": "Авто колонки", "Auto Log Clean": "Автоматическая очистка логов", "Never Clean": "Никогда не очищать", "Retain _n Days": "Сохранять {{n}} дней", "Default Latency Test": "Ссылка на тест задержки", "Default Latency Test Info": "Используется только для тестирования HTTP-запросов клиента и не влияет на файл конфигурации", "Default Latency Timeout": "Таймаут задержки по умолчанию", "Hotkey Setting": "Настройки сочетаний клавиш", "Enable Global Hotkey": "Включить глобальную горячую клавишу", "open_or_close_dashboard": "Открыть/Закрыть панель управления", "clash_mode_rule": "Режим правил", "clash_mode_global": "Глобальный режим", "clash_mode_direct": "Прямой режим", "toggle_system_proxy": "Включить/Отключить системный прокси", "toggle_tun_mode": "Включить/Отключить режим TUN", "entry_lightweight_mode": "Вход в LightWeight Mode", "Backup Setting": "Настройки резервного копирования", "Backup Setting Info": "Поддерживает файлы конфигурации резервного копирования WebDAV", "Runtime Config": "Используемый конфиг", "Open Conf Dir": "Открыть папку приложения", "Open Conf Dir Info": "Если программное обеспечение работает неправильно, сделайте резервную копию и удалите все файлы в этой папке, а затем перезапустите ПО", "Open Core Dir": "Открыть папку ядра", "Open Logs Dir": "Открыть папку логов", "Check for Updates": "Проверить обновления", "Go to Release Page": "Перейти на страницу релизов", "Portable Updater Error": "Портативная версия не поддерживает обновление внутри приложения, пожалуйста, скачайте и замените файлы вручную", "Break Change Update Error": "Это крупное обновление, которое не поддерживает обновление внутри приложения. Пожалуйста, удалите его и загрузите установочный файл вручную.", "Open Dev Tools": "Открыть Dev Tools", "Export Diagnostic Info": "Экспорт диагностической информации", "Export Diagnostic Info For Issue Reporting": "Экспорт диагностической информации для отчета об ошибке", "Exit": "Выход", "Verge Version": "Версия Clash Verge Rev", "ReadOnly": "Только для чтения", "ReadOnlyMessage": "Невозможно редактировать в режиме только для чтения", "Filter": "Фильтр", "Filter conditions": "Условия фильтрации", "Match Case": "Учитывать регистр", "Match Whole Word": "Полное совпадение слова", "Use Regular Expression": "Использовать регулярные выражения", "Profile Imported Successfully": "Профиль успешно импортирован", "Profile Switched": "Профиль изменен", "Profile Reactivated": "Профиль перезапущен", "Only YAML Files Supported": "Поддерживаются только файлы YAML", "Settings Applied": "Настройки применены", "Installing Service...": "Установка службы...", "Service Installed Successfully": "Служба успешно установлена", "Service Uninstalled Successfully": "Служба успешно удалена", "Proxy Daemon Duration Cannot be Less than 1 Second": "Продолжительность работы прокси-демона не может быть меньше 1 секунды", "Invalid Bypass Format": "Неверный формат обхода", "Clash Port Modified": "Порт Clash изменен", "Port Conflict": "Конфликт портов", "Restart Application to Apply Modifications": "Чтобы изменения вступили в силу, необходимо перезапустить приложение", "External Controller Address Modified": "Настройки внешнего контроллера изменены", "Permissions Granted Successfully for _clash Core": "Разрешения успешно предоставлены для ядра {{core}}", "Core Version Updated": "Ядро обновлено до последней версии", "Clash Core Restarted": "Ядро перезапущено", "GeoData Updated": "Файлы GeoData обновлены", "Currently on the Latest Version": "Обновление не требуется", "Import Subscription Successful": "Подписка успешно импортирована", "WebDAV Server URL": "URL-адрес сервера WebDAV http(s)://", "Username": "Имя пользователя", "Password": "Пароль", "Backup": "Резервное копирование", "Filename": "Имя файла", "Actions": "Действия", "Restore": "Восстановить", "No Backups": "Нет доступных резервных копий", "WebDAV URL Required": "URL-адрес WebDAV не может быть пустым", "Invalid WebDAV URL": "Неверный формат URL-адреса WebDAV", "Username Required": "Имя пользователя не может быть пустым", "Password Required": "Пароль не может быть пустым", "Failed to Fetch Backups": "Не удалось получить файлы резервных копий", "WebDAV Config Saved": "Конфигурация WebDAV успешно сохранена", "WebDAV Config Save Failed": "Не удалось сохранить конфигурацию WebDAV: {{error}}", "Backup Created": "Резервная копия успешно создана", "Backup Failed": "Ошибка резервного копирования: {{error}}", "Delete Backup": "Удалить резервную копию", "Restore Backup": "Восстановить резервную копию", "Backup Time": "Время резервного копирования", "Confirm to delete this backup file?": "Вы уверены, что хотите удалить этот файл резервной копии?", "Confirm to restore this backup file?": "Вы уверены, что хотите восстановить этот файл резервной копии?", "Restore Success, App will restart in 1s": "Восстановление успешно выполнено, приложение перезапустится через 1 секунду", "Failed to fetch backup files": "Не удалось получить файлы резервных копий", "Profile": "Профиль", "Help": "Помощь", "About": "О программе", "Theme": "Тема", "Main Window": "Главное окно", "Group Icon": "Иконка группы", "Menu Icon": "Иконка меню", "PAC File": "PAC файл", "Web UI": "Веб-интерфейс", "Hotkeys": "Горячие клавиши", "Verge Mixed Port": "Mixed порт", "Verge Socks Port": "Порт Socks", "Verge Redir Port": "Порт Redir", "Verge Tproxy Port": "Порт Tproxy", "Verge Port": "Порт Verge", "Verge HTTP Enabled": "HTTP включен", "WebDAV URL": "URL WebDAV", "WebDAV Username": "Имя пользователя WebDAV", "WebDAV Password": "Пароль WebDAV", "Dashboard": "Панель управления", "Restart App": "Перезапустить приложение", "Restart Clash Core": "Перезапустить ядро Clash", "TUN Mode": "Режим TUN", "Copy Env": "Копировать переменные окружения", "Conf Dir": "Директория конфигурации", "Core Dir": "Директория ядра", "Logs Dir": "Директория логов", "Open Dir": "Открыть директорию", "More": "<PERSON><PERSON><PERSON>", "Rule Mode": "Режим правил", "Global Mode": "Глобальный режим", "Direct Mode": "Прямой режим", "Enable Tray Speed": "Показывать скорость в трее", "Enable Tray Icon": "Показывать значок в трее", "LightWeight Mode": "LightWeight Mode", "LightWeight Mode Info": "Режим, в котором работает только ядро Clash, а графический интрефейс закрыт", "LightWeight Mode Settings": "Настройки LightWeight Mode", "Enter LightWeight Mode Now": "Войти в LightWeight Mode", "Auto Enter LightWeight Mode": "Автоматический вход в LightWeight Mode", "Auto Enter LightWeight Mode Info": "Автоматически включать LightWeight Mode, если окно закрыто определенное время", "Auto Enter LightWeight Mode Delay": "Задержка включения LightWeight Mode", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "При закрытии окна LightWeight Mode будет автоматически активирован через {{n}} минут", "Config Validation Failed": "Ошибка проверки конфигурации подписки, проверьте файл конфигурации, изменения отменены, ошибка:", "Boot Config Validation Failed": "Ошибка проверки конфигурации при запуске, используется конфигурация по умолчанию, проверьте файл конфигурации, ошибка:", "Core Change Config Validation Failed": "Ошибка проверки конфигурации при смене ядра, используется конфигурация по умолчанию, проверьте файл конфигурации, ошибка:", "Config Validation Process Terminated": "Процесс проверки прерван", "Script Syntax Error": "Ошибка синтаксиса скрипта, изменения отменены", "Script Missing Main": "Ошибка скрипта, изменения отменены", "File Not Found": "Файл не найден, изменения отменены", "Script File Error": "Ошибка файла скрипта, изменения отменены", "Core Changed Successfully": "Ядро успешно изменено", "Failed to Change Core": "Не удалось сменить ядро", "YAML Syntax Error": "Ошибка синтаксиса YAML, откат изменений", "YAML Read Error": "Ошибка чтения YAML, откат изменений", "YAML Mapping Error": "Ошибка YAML Mapping, откат изменений", "YAML Key Error": "Ошибка ключа YAML, откат изменений", "YAML Error": "Ошибка YAML, откат изменений", "Merge File Syntax Error": "Ошибка синтаксиса Merge File, откат изменений", "Merge File Mapping Error": "Ошибка сопоставления в Merge File, откат изменений", "Merge File Key Error": "Ошибка ключа в Merge File, откат изменений", "Merge File Error": "Ошибка Merge File, откат изменений", "Validate YAML File": "Проверить YAML файл", "Validate Merge File": "Проверить Merge File", "Validation Success": "Файл успешно проверен", "Validation Failed": "Проверка не удалась", "Service Administrator Prompt": "Clash Verge требует прав администратора для переустановки системной службы", "DNS Settings": "Настройки DNS", "DNS Overwrite": "Переопределение настроек DNS", "DNS Settings Warning": "Если вы не знакомы с этими настройками, пожалуйста, не изменяйте и не отключайте их", "Enable DNS": "Включить DNS", "DNS Listen": "Прослушивание DNS", "Enhanced Mode": "Enhanced Mode", "Fake IP Range": "Диа<PERSON>азон FakeIP", "Fake IP Filter Mode": "FakeIP Filter Mode", "Prefer H3": "Предпочитать H3", "DNS DOH使用HTTP/3": "DNS DOH использует http/3", "Respect Rules": "Приоритизировать правила", "DNS connections follow routing rules": "Соединения DNS следуют правилам маршрутизации", "Use Hosts": "Использовать файл Hosts", "Enable to resolve hosts through hosts file": "Включить разрешение хостов через файл Hosts", "Use System Hosts": "Использовать системный файл Hosts", "Enable to resolve hosts through system hosts file": "Включить разрешение хостов через системный файл Hosts", "Direct Nameserver Follow Policy": "Прямой сервер имен следует политике", "Whether to follow nameserver policy": "Следовать ли политике DNS-серверов", "Default Nameserver": "DNS-сервер по умолчанию", "Default DNS servers used to resolve DNS servers": "DNS-серверы по умолчанию, используемые для разрешения адресов серверов DNS", "Nameserver": "DNS-сервер", "List of DNS servers": "Список DNS-серверов, разделенных запятой", "Fallback": "Fallback", "List of fallback DNS servers": "Список резервных DNS-серверов, разделенных запятой", "Proxy Server Nameserver": "Proxy Server Nameserver", "Proxy Node Nameserver": "DNS-серверы для разрешения домена прокси-узлов", "Direct Nameserver": "DNS-сервер для прямых соединений", "Direct outbound Nameserver": "Список DNS-серверов для прямых соединений, разделенных запятой", "Fake IP Filter": "Фильтр FakeIP", "Domains that skip fake IP resolution": "Домены, которые пропускают разрешение FakeIP, разделенные запятой", "Nameserver Policy": "Политика серверов имен", "Domain-specific DNS server": "DNS-сервер, специфичный для домена, несколько серверов разделяются знаком ';'", "Fallback Filter Settings": "Настройки фильтра Fallback", "GeoIP Filtering": "Фильтрация GeoIP", "Enable GeoIP filtering for fallback": "Включить фильтрацию GeoIP", "GeoIP Code": "Код GeoIP", "Fallback IP CIDR": "Fallback IP CIDR", "IP CIDRs not using fallback servers": "Диапазоны IP-адрес<PERSON>, не использующие резервные серверы, разделенные запятой", "Fallback Domain": "Fallback домены", "Domains using fallback servers": "Домены, использующие резервные серверы, разделенные запятой", "Enable Alpha Channel": "Включить альфа-канал", "Alpha versions may contain experimental features and bugs": "Альфа-версии могут содержать экспериментальные функции и ошибки", "Home Settings": "Настройки главной страницы", "Profile Card": "Карточка профиля", "Current Proxy Card": "Карточка текущего прокси", "Network Settings Card": "Карточка настроек сети", "Proxy Mode Card": "Карточка режима работы", "Clash Mode Card": "Карточка режима Clash", "Traffic Stats Card": "Карточка статистики по трафику", "Clash Info Cards": "Информация о Clash", "System Info Cards": "Информация о системе", "Website Tests Card": "Карточка тестов доступности веб-сайтов", "Traffic Stats": "Статистика по трафику", "Website Tests": "Проверка доступности веб-сайтов", "Clash Info": "Информация о Clash", "Core Version": "Версия ядра", "System Proxy Address": "Адрес системного прокси", "Uptime": "Время работы", "Rules Count": "Количество правил", "System Info": "Информация о системе", "OS Info": "Версия ОС", "Running Mode": "Режим работы", "Sidecar Mode": "Пользовательский режим", "Last Check Update": "Последняя проверка обновлений", "Click to import subscription": "Нажмите, чтобы импортировать подписку", "Update subscription successfully": "Подписка успешно обновлена", "Current Node": "Текущий сервер", "No active proxy node": "Нет активного прокси-узла", "Network Settings": "Настройки сети", "Proxy Mode": "Режим работы", "Group": "Группа", "Proxy": "Прокси", "IP Information Card": "Информация об IP", "IP Information": "Информация об IP", "Failed to get IP info": "Не удалось получить информацию об IP", "ISP": "ISP", "ASN": "ASN", "ORG": "ORG", "Location": "Location", "Timezone": "Timezone", "Auto refresh": "Автоматическое обновление через", "Unlock Test": "Тест доступности веб-сайтов", "Pending": "В ожидании", "Yes": "Да", "No": "Нет", "Failed": "Ошибка", "Completed": "Завершено", "Disallowed ISP": "ISP заблокирован", "Originals Only": "Только Originals", "No (IP Banned By Disney+)": "Нет (IP забанен Disney+)", "Unsupported Country/Region": "Страна/регион не поддерживается", "Failed (Network Connection)": "Ошибка подключения", "Invalid Profile URL": "Недопустимая ссылка на профиль, введите адрес, начинающийся с http:// или https://"}