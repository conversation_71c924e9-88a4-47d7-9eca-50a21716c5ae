{"millis": "ms", "seconds": "seconds", "mins": "mins", "Back": "Back", "Close": "Close", "Cancel": "Cancel", "Confirm": "Confirm", "Maximize": "Maximize", "Minimize": "Minimize", "Format document": "Format document", "Empty": "Empty", "New": "New", "Edit": "Edit", "Save": "Save", "Delete": "Delete", "Enable": "Enable", "Disable": "Disable", "Label-Home": "Home", "Label-Proxies": "Proxies", "Label-Profiles": "Profiles", "Label-Connections": "Connections", "Label-Rules": "Rules", "Label-Logs": "Logs", "Label-Unlock": "Test", "Label-Settings": "Settings", "Proxies": "Proxies", "Proxy Groups": "Proxy Groups", "Proxy Provider": "Proxy Provider", "Proxy Count": "Proxy Count", "Update All": "Update All", "Update At": "Update At", "rule": "rule", "global": "global", "direct": "direct", "script": "script", "locate": "locate", "Delay check": "Delay check", "Sort by default": "Sort by default", "Sort by delay": "Sort by delay", "Sort by name": "Sort by name", "Delay check URL": "Delay check URL", "Delay check to cancel fixed": "Delay check to cancel fixed", "Proxy basic": "Proxy basic", "Proxy detail": "Proxy detail", "Profiles": "Profiles", "Update All Profiles": "Update All Profiles", "View Runtime Config": "View Runtime Config", "Reactivate Profiles": "Reactivate Profiles", "Paste": "Paste", "Profile URL": "Profile URL", "Import": "Import", "From": "From", "Update Time": "Update Time", "Used / Total": "Used / Total", "Expire Time": "Expire Time", "Create Profile": "Create Profile", "Edit Profile": "Edit Profile", "Edit Proxies": "Edit Proxies", "Use newlines for multiple uri": "Use newlines for multiple uri(Base64 encoding supported)", "Edit Rules": "Edit Rules", "Rule Type": "Rule Type", "Rule Content": "Rule Content", "Proxy Policy": "Proxy Policy", "No Resolve": "No Resolve", "Prepend Rule": "Prepend Rule", "Append Rule": "Append Rule", "Prepend Group": "Prepend Group", "Append Group": "Append Group", "Prepend Proxy": "Prepend Proxy", "Append Proxy": "Append Proxy", "Rule Condition Required": "Rule Condition Required", "Invalid Rule": "Invalid Rule", "Advanced": "Advanced", "Visualization": "Visualization", "DOMAIN": "Matches the full domain name", "DOMAIN-SUFFIX": "Matches the domain suffix", "DOMAIN-KEYWORD": "Matches the domain keyword", "DOMAIN-REGEX": "Matches the domain using regular expressions", "GEOSITE": "Matches domains within the Geosite", "GEOIP": "Matches the country code of the IP address", "SRC-GEOIP": "Matches the country code of the source IP address", "IP-ASN": "Matches the IP address's ASN", "SRC-IP-ASN": "Matches the source IP address's ASN", "IP-CIDR": "Matches the IP address range", "IP-CIDR6": "Matches the IPv6 address range", "SRC-IP-CIDR": "Matches the source IP address range", "IP-SUFFIX": "Matches the IP address suffix range", "SRC-IP-SUFFIX": "Matches the source IP address suffix range", "SRC-PORT": "Matches the source port range", "DST-PORT": "Matches the destination port range", "IN-PORT": "Matches the inbound port", "DSCP": "DSCP marking (only for tproxy UDP inbound)", "PROCESS-NAME": "Matches the process name (Android package name)", "PROCESS-PATH": "Matches the full process path", "PROCESS-NAME-REGEX": "Matches the full process name using regular expressions (Android package name)", "PROCESS-PATH-REGEX": "Matches the full process path using regular expressions", "NETWORK": "Matches the transport protocol (tcp/udp)", "UID": "Matches the Linux USER ID", "IN-TYPE": "Matches the inbound type", "IN-USER": "Matches the inbound username", "IN-NAME": "Matches the inbound name", "SUB-RULE": "Sub-rule", "RULE-SET": "Matches the rule set", "AND": "Logical AND", "OR": "Logical OR", "NOT": "Logical NOT", "MATCH": "Matches all requests", "DIRECT": "Data goes directly outbound", "REJECT": "Intercepts requests", "REJECT-DROP": "Discards requests", "PASS": "Skips this rule when matched", "Edit Groups": "Edit Proxy Groups", "Group Type": "Group Type", "select": "Select proxy manually", "url-test": "Select proxy based on URL test delay", "fallback": "Switch to another proxy on error", "load-balance": "Distribute proxy based on load balancing", "relay": "Pass through the defined proxy chain", "Group Name": "Group Name", "Use Proxies": "Use Proxies", "Use Provider": "Use Provider", "Health Check Url": "Health Check Url", "Expected Status": "Expected Status", "Interval": "Interval", "Lazy": "Lazy", "Timeout": "Timeout", "Max Failed Times": "Max Failed Times", "Interface Name": "Interface Name", "Routing Mark": "Routing <PERSON>", "Include All": "Include All Proxies and Providers", "Include All Providers": "Include All Providers", "Include All Proxies": "Include All Proxies", "Exclude Filter": "Exclude Filter", "Exclude Type": "Exclude Type", "Disable UDP": "Disable UDP", "Hidden": "Hidden", "Group Name Required": "Group Name Required", "Group Name Already Exists": "Group Name Already Exists", "Extend Config": "Extend Config", "Extend Script": "Extend <PERSON>", "Global Merge": "Global Extend Config", "Global Script": "Global Extend Script", "Type": "Type", "Name": "Name", "Descriptions": "Descriptions", "Subscription URL": "Subscription URL", "Update Interval": "Update Interval", "Choose File": "Choose <PERSON>", "Use System Proxy": "Use System Proxy", "Use Clash Proxy": "Use Clash Proxy", "Accept Invalid Certs (Danger)": "Allows Invalid Certificates (Danger)", "Refresh": "Refresh", "Home": "Home", "Select": "Select", "Edit Info": "Edit Info", "Edit File": "Edit File", "Open File": "Open File", "Update": "Update", "Update via proxy": "Update via proxy", "Update(Proxy)": "Update(Proxy)", "Confirm deletion": "Confirm deletion", "This operation is not reversible": "This operation is not reversible", "Script Console": "<PERSON><PERSON><PERSON>", "To Top": "To Top", "To End": "To End", "Connections": "Connections", "Table View": "Table View", "List View": "List View", "Close All": "Close All", "Upload": "Upload", "Download": "Download", "Download Speed": "Download Speed", "Upload Speed": "Upload Speed", "Host": "Host", "Downloaded": "Downloaded", "Uploaded": "Uploaded", "DL Speed": "DL Speed", "UL Speed": "UL Speed", "Active Connections": "Active Connections", "Chains": "Chains", "Rule": "Rule", "Process": "Process", "Time": "Time", "Source": "Source", "Destination": "Destination", "DestinationPort": "Destination Port", "Close Connection": "Close Connection", "Rules": "Rules", "Rule Provider": "Rule Provider", "Logs": "Logs", "Pause": "Pause", "Resume": "Resume", "Clear": "Clear", "Test": "Test", "Test All": "Test All", "Testing...": "Testing...", "Create Test": "Create Test", "Edit Test": "Edit Test", "Icon": "Icon", "Test URL": "Test URL", "Settings": "Settings", "System Setting": "System Setting", "Tun Mode": "Tun <PERSON>", "TUN requires Service Mode": "TUN mode requires install service", "Install Service": "Install Service", "Install Service failed": "Install Service failed", "Uninstall Service": "Uninstall Service", "Restart Core failed": "Restart Core failed", "Reset to Default": "Reset to De<PERSON>ult", "Tun Mode Info": "Tun (Virtual NIC) mode: Captures all system traffic, when enabled, there is no need to enable system proxy.", "TUN requires Service Mode or Admin Mode": "TUN requires Service Mode or Admin Mode", "System Proxy Enabled": "System proxy is enabled, your applications will access the network through the proxy", "System Proxy Disabled": "System proxy is disabled, it is recommended for most users to turn on this option", "TUN Mode Enabled": "TUN mode is enabled, applications will access the network through the virtual network card", "TUN Mode Disabled": "TUN mode is disabled, suitable for special applications", "TUN Mode Service Required": "TUN mode requires service mode, please install the service first", "TUN Mode Intercept Info": "TUN mode can take over all application traffic, suitable for special applications that do not follow the system proxy settings", "Core communication error": "Core communication error", "Rule Mode Description": "Routes traffic according to preset rules, provides flexible proxy strategies", "Global Mode Description": "All traffic goes through proxy servers, suitable for scenarios requiring global internet access", "Direct Mode Description": "All traffic doesn't go through proxy nodes, but is forwarded by Clash kernel to target servers, suitable for specific scenarios requiring kernel traffic distribution", "Stack": "<PERSON><PERSON>", "System and Mixed Can Only be Used in Service Mode": "System and Mixed Can Only be Used in Service Mode", "Device": "Device Name", "Auto Route": "Auto Route", "Strict Route": "Strict Route", "Auto Detect Interface": "Auto Detect Interface", "DNS Hijack": "DNS Hijack", "MTU": "Max Transmission Unit", "Service Mode": "Service Mode", "Service Mode Info": "Please install the service mode before enabling TUN mode. The kernel process started by the service can obtain the permission to install the virtual network card (TUN mode)", "Current State": "Current State", "pending": "pending", "installed": "installed", "uninstall": "uninstalled", "active": "active", "unknown": "unknown", "Information: Please make sure that the Clash Verge Service is installed and enabled": "Information: Please make sure that the Clash Verge Service is installed and enabled", "Install": "Install", "Uninstall": "Uninstall", "Disable Service Mode": "Disable Service Mode", "System Proxy": "System Proxy", "System Proxy Info": "Enable to modify the operating system's proxy settings. If enabling fails, modify the operating system's proxy settings manually", "System Proxy Setting": "System Proxy Setting", "Current System Proxy": "Current System Proxy", "Enable status": "Enable Status:", "Enabled": "Enabled", "Disabled": "Disabled", "Server Addr": "Server Addr: ", "Proxy Host": "Proxy Host", "Invalid Proxy Host Format": "Invalid Proxy Host Format", "Not available": "Not available", "Proxy Guard": "Proxy Guard", "Proxy Guard Info": "Enable to prevent other software from modifying the operating system's proxy settings", "Guard Duration": "Guard Duration", "Always use Default Bypass": "Always use Default Bypass", "Use Bypass Check": "Use Bypass Check", "Proxy Bypass": "Proxy Bypass Settings: ", "Bypass": "Bypass: ", "Use PAC Mode": "Use PAC Mode", "PAC Script Content": "PAC Script Content", "PAC URL": "PAC URL: ", "Auto Launch": "Auto Launch", "Administrator mode may not support auto launch": "Administrator mode may not support auto launch", "Silent Start": "Silent Start", "Silent Start Info": "Start the program in background mode without displaying the panel", "Hover Jump Navigator": "Hover Jump Navigator", "Hover Jump Navigator Info": "Automatically scroll to the corresponding proxy group when hovering over alphabet letters", "TG Channel": "Telegram Channel", "Manual": "Manual", "Github Repo": "<PERSON><PERSON><PERSON>", "Clash Setting": "<PERSON><PERSON> Setting", "Allow Lan": "Allow LAN", "Network Interface": "Network Interface", "Ip Address": "IP Address", "Mac Address": "MAC Address", "IPv6": "IPv6", "Unified Delay": "Unified Delay", "Unified Delay Info": "When unified delay is turned on, two delay tests will be performed to eliminate the delay differences between different types of nodes caused by connection handshakes, etc", "Log Level": "Log Level", "Log Level Info": "This parameter is valid only for kernel log files in the log directory Service folder", "Port Config": "Port Config", "Random Port": "Random Port", "Mixed Port": "Mixed Port", "Socks Port": "Socks Port", "Http Port": "Http(s) Port", "Redir Port": "Redir Port", "Tproxy Port": "Tproxy Port", "External": "External", "Enable External Controller": "Enable External Controller", "External Controller": "External Controller", "Core Secret": "Core Secret", "Recommended": "Recommended", "Open URL": "Open URL", "Replace host, port, secret with %host, %port, %secret": "Replace host, port, secret with %host, %port, %secret", "Support %host, %port, %secret": "Support %host, %port, %secret", "Clash Core": "Clash Core", "Upgrade": "Upgrade", "Restart": "<PERSON><PERSON>", "Release Version": "Release Version", "Alpha Version": "Alpha Version", "Please Enable Service Mode": "Please Install and Enable Service Mode First", "Please enter your root password": "Please enter your root password", "Grant": "<PERSON>", "Open UWP tool": "Open UWP tool", "Open UWP tool Info": "Since Windows 8, UWP apps (such as Microsoft Store) are restricted from directly accessing local host network services, and this tool can be used to bypass this restriction", "Update GeoData": "Update GeoData", "Verge Basic Setting": "Verge Basic Setting", "Verge Advanced Setting": "Verge Advanced Setting", "Language": "Language", "Theme Mode": "Theme Mode", "theme.light": "Light", "theme.dark": "Dark", "theme.system": "System", "Tray Click Event": "<PERSON><PERSON>lick Event", "Show Main Window": "Show Main Window", "Show Tray Menu": "Show Tray Menu", "Copy Env Type": "Copy Env Type", "Copy Success": "Copy Success", "Start Page": "Start Page", "Startup Script": "Startup <PERSON>", "Browse": "Browse", "Theme Setting": "Theme Setting", "Primary Color": "Primary Color", "Secondary Color": "Secondary Color", "Primary Text": "Primary Text", "Secondary Text": "Secondary Text", "Info Color": "Info Color", "Warning Color": "Warning Color", "Error Color": "Error Color", "Success Color": "Success Color", "Font Family": "Font Family", "CSS Injection": "CSS Injection", "Layout Setting": "Layout Setting", "Traffic Graph": "Traffic Graph", "Memory Usage": "Core Usage", "Memory Cleanup": "Tap to clean up memory", "Proxy Group Icon": "Proxy Group Icon", "Nav Icon": "Nav Icon", "Monochrome": "Monochrome", "Colorful": "Colorful", "Tray Icon": "Tray Icon", "Common Tray Icon": "Common Tray Icon", "System Proxy Tray Icon": "System Proxy Tray Icon", "Tun Tray Icon": "<PERSON><PERSON>", "Miscellaneous": "Miscellaneous", "App Log Level": "App Log Level", "Auto Close Connections": "Auto Close Connections", "Auto Close Connections Info": "Terminate established connections when the proxy group selection or proxy mode changes", "Auto Check Update": "Auto Check Update", "Enable Builtin Enhanced": "Enable Builtin Enhanced", "Enable Builtin Enhanced Info": "Compatibility handling for the configuration file", "Proxy Layout Columns": "Proxy Layout Columns", "Auto Columns": "Auto Columns", "Auto Log Clean": "Auto Log Clean", "Never Clean": "Never Clean", "Retain _n Days": "Retain {{n}} Days", "Default Latency Test": "Default Latency Test", "Default Latency Test Info": "Used for HTTP client request testing only and won't make a difference to the configuration file", "Default Latency Timeout": "Default Latency Timeout", "Hotkey Setting": "<PERSON><PERSON> Setting", "Enable Global Hotkey": "Enable Global Hotkey", "open_or_close_dashboard": "Open/Close Dashboard", "clash_mode_rule": "Rule Mode", "clash_mode_global": "Global Mode", "clash_mode_direct": "Direct Mode", "toggle_system_proxy": "Enable/Disable System Proxy", "toggle_tun_mode": "Enable/Disable Tun Mode", "entry_lightweight_mode": "Entry Lightweight Mode", "Backup Setting": "Backup Setting", "Backup Setting Info": "Support WebDAV backup configuration files", "Runtime Config": "Runtime Config", "Open Conf Dir": "Open Conf Dir", "Open Conf Dir Info": "If the software runs abnormally, BACKUP and delete all files in this folder then restart the software", "Open Core Dir": "Open Core Dir", "Open Logs Dir": "Open Logs Dir", "Check for Updates": "Check for Updates", "Go to Release Page": "Go to Release Page", "Portable Updater Error": "The portable version does not support in-app updates. Please manually download and replace it", "Break Change Update Error": "This version is a major update and does not support in-app updates. Please uninstall it and manually download and install the new version", "Open Dev Tools": "<PERSON>", "Export Diagnostic Info": "Export Diagnostic Info", "Export Diagnostic Info For Issue Reporting": "Export Diagnostic Info For Issue Reporting", "Exit": "Exit", "Verge Version": "Verge Version", "ReadOnly": "Read<PERSON>nly", "ReadOnlyMessage": "Cannot edit in read-only editor", "Filter": "Filter", "Filter conditions": "Filter conditions", "Match Case": "Match Case", "Match Whole Word": "Match Whole Word", "Use Regular Expression": "Use Regular Expression", "Profile Imported Successfully": "Profile Imported Successfully", "Profile Switched": "Profile Switched", "Profile Reactivated": "Profile Reactivated", "Profile switch interrupted by new selection": "Profile switch interrupted by new selection", "Only YAML Files Supported": "Only YAML Files Supported", "Settings Applied": "Settings Applied", "Stopping Core...": "Stopping Core...", "Restarting Core...": "Restarting Core...", "Installing Service...": "Installing Service...", "Uninstalling Service...": "Uninstalling Service...", "Service Installed Successfully": "Service Installed Successfully", "Service Uninstalled Successfully": "Service Uninstalled Successfully", "Proxy Daemon Duration Cannot be Less than 1 Second": "Proxy Daemon Duration Cannot be Less than 1 Second", "Invalid Bypass Format": "Invalid Bypass Format", "Waiting for service to be ready...": "Waiting for service to be ready...", "Service not ready, retrying attempt {count}/{total}...": "Service not ready, retrying attempt {{count}}/{{total}}...", "Failed to check service status, retrying attempt {count}/{total}...": "Failed to check service status, retrying attempt {{count}}/{{total}}...", "Service did not become ready after attempts. Proceeding with core restart.": "Service did not become ready after attempts. Proceeding with core restart.", "Service was ready, but core restart might have issues or service became unavailable. Please check.": "Service was ready, but core restart might have issues or service became unavailable. Please check.", "Service installation or core restart encountered issues. Service might not be available. Please check system logs.": "Service installation or core restart encountered issues. Service might not be available. Please check system logs.", "Attempting to restart core as a fallback...": "Attempting to restart core as a fallback...", "Fallback core restart also failed: {message}": "Fallback core restart also failed: {{message}}", "Service is ready and core restarted": "Service is ready and core restarted", "Core restarted. Service is now available.": "Core restarted. Service is now available.", "Clash Port Modified": "Clash Port Modified", "Port Conflict": "Port Conflict", "Restart Application to Apply Modifications": "Restart Application to Apply Modifications", "External Controller Address Modified": "External Controller Address Modified", "Permissions Granted Successfully for _clash Core": "Permissions Granted Successfully for {{core}} Core", "Core Version Updated": "Core Version Updated", "Clash Core Restarted": "Clash Core Restarted", "GeoData Updated": "GeoData Updated", "Currently on the Latest Version": "Currently on the Latest Version", "Already Using Latest Core": "Already Using Latest Core", "Import Subscription Successful": "Import subscription successful", "WebDAV Server URL": "WebDAV Server URL", "Username": "Username", "Password": "Password", "Backup": "Backup", "Filename": "Filename", "Actions": "Actions", "Restore": "Rest<PERSON>", "No Backups": "No backups available", "WebDAV URL Required": "WebDAV URL cannot be empty", "Invalid WebDAV URL": "Invalid WebDAV URL format", "Username Required": "Username cannot be empty", "Password Required": "Password cannot be empty", "Failed to Fetch Backups": "Failed to fetch backup files", "WebDAV Config Saved": "WebDAV configuration saved successfully", "WebDAV Config Save Failed": "Failed to save WebDAV configuration: {{error}}", "Backup Created": "Backup created successfully", "Backup Failed": "Backup failed: {{error}}", "Delete Backup": "Delete Backup", "Restore Backup": "Restore Backup", "Backup Time": "Backup Time", "Confirm to delete this backup file?": "Confirm to delete this backup file?", "Confirm to restore this backup file?": "Confirm to restore this backup file?", "Restore Success, App will restart in 1s": "Restore Success, App will restart in 1s", "Failed to fetch backup files": "Failed to fetch backup files", "Profile": "Profile", "Help": "Help", "About": "About", "Theme": "Theme", "Main Window": "Main Window", "Group Icon": "Group Icon", "Menu Icon": "Menu Icon", "PAC File": "PAC File", "Web UI": "Web UI", "Hotkeys": "Hotkeys", "Verge Mixed Port": "Verge Mixed Port", "Verge Socks Port": "Verge Socks Port", "Verge Redir Port": "Verge Redir Port", "Verge Tproxy Port": "Verge Tproxy Port", "Verge Port": "Verge Port", "Verge HTTP Enabled": "Verge HTTP Enabled", "WebDAV URL": "WebDAV URL", "WebDAV Username": "WebDAV Username", "WebDAV Password": "WebDAV Password", "Dashboard": "Dashboard", "Restart App": "Restart App", "Restart Clash Core": "<PERSON><PERSON>", "TUN Mode": "TUN Mode", "Copy Env": "Copy Env", "Conf Dir": "<PERSON><PERSON>", "Core Dir": "<PERSON>", "Logs Dir": "Logs Dir", "Open Dir": "Open Dir", "More": "More", "Rule Mode": "Rule Mode", "Global Mode": "Global Mode", "Direct Mode": "Direct Mode", "Enable Tray Speed": "Enable Tray Speed", "Enable Tray Icon": "Enable Tray Icon", "LightWeight Mode": "Lightweight Mode", "LightWeight Mode Info": "Close the GUI and keep only the kernel running", "LightWeight Mode Settings": "LightWeight Mode Settings", "Enter LightWeight Mode Now": "Enter LightWeight Mode Now", "Auto Enter LightWeight Mode": "Auto Enter LightWeight Mode", "Auto Enter LightWeight Mode Info": "Enable to automatically activate LightWeight Mode after the window is closed for a period of time", "Auto Enter LightWeight Mode Delay": "Auto Enter LightWeight Mode Delay", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "When closing the window, LightWeight Mode will be automatically activated after {{n}} minutes", "Config Validation Failed": "Subscription configuration validation failed. Please check the subscription configuration file; modifications have been rolled back.", "Boot Config Validation Failed": "Boot subscription configuration validation failed. Started with the default configuration; please check the subscription configuration file.", "Core Change Config Validation Failed": "Configuration validation failed when switching the kernel. Started with the default configuration; please check the subscription configuration file.", "Config Validation Process Terminated": "The validation process has been terminated.", "Script Syntax Error": "Script syntax error, changes reverted", "Script Missing Main": "<PERSON><PERSON><PERSON> error, changes reverted", "File Not Found": "File missing, changes reverted", "Script File Error": "Script file error, changes reverted", "Core Changed Successfully": "Core changed successfully", "Failed to Change Core": "Failed to change core", "YAML Syntax Error": "YAML syntax error, changes reverted", "YAML Read Error": "YAML read error, changes reverted", "YAML Mapping Error": "YAML mapping error, changes reverted", "YAML Key Error": "YAML key error, changes reverted", "YAML Error": "YAML error, changes reverted", "Merge File Syntax Error": "Merge file syntax error, changes reverted", "Merge File Mapping Error": "Merge file mapping error, changes reverted", "Merge File Key Error": "Merge file key error, changes reverted", "Merge File Error": "Merge file error, changes reverted", "Validate YAML File": "Validate YAML File", "Validate Merge File": "Validate Merge File", "Validation Success": "Validation Success", "Validation Failed": "Validation Failed", "Service Administrator Prompt": "Clash Verge requires administrator privileges to reinstall the system service", "DNS Settings": "DNS Settings", "DNS settings saved": "DNS settings saved", "DNS Overwrite": "DNS Overwrite", "DNS Settings Warning": "If you are not familiar with these settings, please do not modify them and keep DNS Overwrite enabled", "Enable DNS": "Enable DNS", "DNS Listen": "DNS Listen", "Enhanced Mode": "Enhanced Mode", "Fake IP Range": "Fake IP Range", "Fake IP Filter Mode": "Fake IP Filter Mode", "Enable IPv6 DNS resolution": "Enable IPv6 DNS resolution", "Prefer H3": "Prefer H3", "DNS DOH使用HTTP/3": "DNS DOH uses HTTP/3", "Respect Rules": "Respect Rules", "DNS connections follow routing rules": "DNS connections follow routing rules", "Use Hosts": "Use Hosts", "Enable to resolve hosts through hosts file": "Enable to resolve hosts through hosts file", "Use System Hosts": "Use System Hosts", "Enable to resolve hosts through system hosts file": "Enable to resolve hosts through system hosts file", "Direct Nameserver Follow Policy": "Direct Nameserver Follow Policy", "Whether to follow nameserver policy": "Whether to follow nameserver policy", "Default Nameserver": "<PERSON><PERSON><PERSON>r", "Default DNS servers used to resolve DNS servers": "Default DNS servers used to resolve DNS servers", "Nameserver": "Nameserver", "List of DNS servers": "List of DNS servers, comma separated", "Fallback": "Fallback", "List of fallback DNS servers": "List of fallback DNS servers, comma separated", "Proxy Server Nameserver": "Proxy Server Nameserver", "Proxy Node Nameserver": "DNS servers for proxy node domain resolution", "Direct Nameserver": "Direct Nameserver", "Direct outbound Nameserver": "DNS servers for direct exit domain resolution, supports 'system' keyword, comma separated", "Fake IP Filter": "Fake IP Filter", "Domains that skip fake IP resolution": "Domains that skip fake IP resolution, comma separated", "Nameserver Policy": "Nameserver Policy", "Domain-specific DNS server": "Domain-specific DNS server, multiple servers separated by semicolons, format: domain=server1;server2", "Fallback Filter Settings": "Fallback Filter <PERSON>", "GeoIP Filtering": "GeoIP Filtering", "Enable GeoIP filtering for fallback": "Enable GeoIP filtering for fallback", "GeoIP Code": "GeoIP Code", "Fallback IP CIDR": "Fallback IP CIDR", "IP CIDRs not using fallback servers": "IP CIDRs not using fallback servers, comma separated", "Fallback Domain": "Fallback Domain", "Domains using fallback servers": "Domains using fallback servers, comma separated", "Hosts Settings": "Hosts Settings", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "Custom domain to IP or domain mapping", "Enable Alpha Channel": "Enable Alpha Channel", "Alpha versions may contain experimental features and bugs": "Alpha versions may contain experimental features and bugs", "Home Settings": "Home Settings", "Profile Card": "Profile Card", "Current Proxy Card": "Current Proxy Card", "Network Settings Card": "Network Settings Card", "Proxy Mode Card": "Proxy Mode Card", "Clash Mode Card": "Clash Mode Card", "Traffic Stats Card": "Traffic Stats Card", "Clash Info Cards": "Clash Info Cards", "System Info Cards": "System Info Cards", "Website Tests Card": "Website Tests Card", "Traffic Stats": "Traffic Stats", "Website Tests": "Website Tests", "Clash Info": "Clash Info", "Core Version": "Core Version", "System Proxy Address": "System Proxy Address", "Uptime": "Uptime", "Rules Count": "Rules Count", "System Info": "System Info", "OS Info": "OS Info", "Running Mode": "Running Mode", "Sidecar Mode": "User Mode", "Administrator Mode": "Administrator Mode", "Administrator + Service Mode": "Admin + Service Mode", "Last Check Update": "Last Check Update", "Click to import subscription": "Click to import subscription", "Last Update failed": "Last Update failed", "Next Up": "Next Up", "No schedule": "No schedule", "Unknown": "Unknown", "Auto update disabled": "Auto update disabled", "Update subscription successfully": "Update subscription successfully", "Update failed, retrying with Clash proxy...": "Update failed, retrying with Clash proxy...", "Update with Clash proxy successfully": "Update with Clash proxy successfully", "Update failed even with Clash proxy": "Update failed even with Clash proxy", "Profile creation failed, retrying with Clash proxy...": "Profile creation failed, retrying with Clash proxy...", "Profile creation succeeded with Clash proxy": "Profile creation succeeded with Clash proxy", "Import failed, retrying with Clash proxy...": "Import failed, retrying with Clash proxy...", "Profile Imported with Clash proxy": "Profile Imported with Clash proxy", "Import failed even with Clash proxy": "Import failed even with Clash proxy", "Current Node": "Current Node", "No active proxy node": "No active proxy node", "Network Settings": "Network Settings", "Proxy Mode": "Proxy Mode", "Group": "Group", "Proxy": "Proxy", "IP Information Card": "IP Information Card", "IP Information": "IP Information", "Failed to get IP info": "Failed to get IP info", "ISP": "ISP", "ASN": "ASN", "ORG": "ORG", "Location": "Location", "Timezone": "Timezone", "Auto refresh": "Auto refresh", "Unlock Test": "Unlock Test", "Pending": "Pending", "Yes": "Yes", "No": "No", "Failed": "Failed", "Completed": "Completed", "Disallowed ISP": "Disallowed ISP", "Originals Only": "Originals Only", "No (IP Banned By Disney+)": "No (IP Banned By Disney+)", "Unsupported Country/Region": "Unsupported Country/Region", "Failed (Network Connection)": "Failed (Network Connection)", "DashboardToggledTitle": "Dashboard Toggled", "DashboardToggledBody": "Dashboard visibility toggled by hotkey", "ClashModeChangedTitle": "Clash Mode Changed", "ClashModeChangedBody": "Switched to {mode} mode", "SystemProxyToggledTitle": "System Proxy Toggled", "SystemProxyToggledBody": "System proxy state toggled by hotkey", "TunModeToggledTitle": "TUN Mode Toggled", "TunModeToggledBody": "TUN mode toggled by hotkey", "LightweightModeEnteredTitle": "Lightweight Mode", "LightweightModeEnteredBody": "Entered lightweight mode by hotkey", "AppQuitTitle": "APP Quit", "AppQuitBody": "APP quit by hotkey", "AppHiddenTitle": "APP Hidden", "AppHiddenBody": "APP window hidden by hotkey", "Invalid Profile URL": "Invalid profile URL. Please enter a URL starting with http:// or https://", "Saved Successfully": "Saved successfully", "External Cors": "External Cors", "Enable one-click CORS for external API. Click to toggle CORS": "Enable one-click CORS for external API. Click to toggle CORS", "External Cors Settings": "External Cors Settings", "External Cors Configuration": "External Cors Configuration", "Allow private network access": "Allow private network access", "Allowed Origins": "Allowed Origins", "Please enter a valid url": "Please enter a valid url", "Add": "Add", "Always included origins: {{urls}}": "Always included origins: {{urls}}", "Invalid regular expression": "Invalid regular expression", "Copy Version": "Copy Version", "Version copied to clipboard": "Version copied to clipboard", "Controller address cannot be empty": "Controller address cannot be empty", "Secret cannot be empty": "Secret cannot be empty", "Configuration saved successfully": "Configuration saved successfully", "Failed to save configuration": "Failed to save configuration", "Controller address copied to clipboard": "Controller address copied to clipboard", "Secret copied to clipboard": "Secret copied to clipboard", "Saving...": "Saving..."}