{"millis": "毫秒", "seconds": "秒", "mins": "分鐘", "Back": "返回", "Close": "關閉", "Cancel": "取消", "Confirm": "確認", "Maximize": "最大化", "Minimize": "最小化", "Format document": "格式化文檔", "Empty": "空空如也", "New": "新建", "Edit": "編輯", "Save": "保存", "Delete": "刪除", "Enable": "啟用", "Disable": "禁用", "Label-Home": "首 頁", "Label-Proxies": "代 理", "Label-Profiles": "訂 閱", "Label-Connections": "連 接", "Label-Rules": "規 則", "Label-Logs": "日 誌", "Label-Unlock": "測 試", "Label-Settings": "設 置", "Proxy Groups": "代理組", "Proxy Provider": "代理集合", "Proxy Count": "節點數量", "Update All": "更新全部", "Update At": "更新於", "rule": "規則", "global": "全局", "direct": "直連", "script": "腳本", "locate": "當前節點", "Delay check": "延遲測試", "Sort by default": "默認排序", "Sort by delay": "按延遲排序", "Sort by name": "按名稱排序", "Delay check URL": "延遲測試鏈接", "Delay check to cancel fixed": "進行延遲測試，以取消固定", "Proxy basic": "隱藏節點細節", "Proxy detail": "展示節點細節", "Profiles": "訂閱", "Update All Profiles": "更新所有訂閱", "View Runtime Config": "查看運行時訂閱", "Reactivate Profiles": "重新激活訂閱", "Paste": "粘貼", "Profile URL": "訂閱文件鏈接", "Import": "導入", "From": "來自", "Update Time": "更新時間", "Used / Total": "已使用 / 總量", "Expire Time": "到期時間", "Create Profile": "新建配置", "Edit Profile": "編輯配置", "Edit Proxies": "編輯節點", "Use newlines for multiple uri": "多條 URI 請使用換行分隔（支持 Base64 編碼）", "Edit Rules": "編輯規則", "Rule Type": "規則類型", "Rule Content": "規則內容", "Proxy Policy": "代理策略", "No Resolve": "跳過 DNS 解析", "Prepend Rule": "添加前置規則", "Append Rule": "添加後置規則", "Prepend Group": "添加前置代理組", "Append Group": "添加後置代理組", "Prepend Proxy": "添加前置代理節點", "Append Proxy": "添加後置代理節點", "Rule Condition Required": "規則條件缺失", "Invalid Rule": "無效規則", "Advanced": "高級", "Visualization": "可視化", "DOMAIN": "匹配完整域名", "DOMAIN-SUFFIX": "匹配域名後綴", "DOMAIN-KEYWORD": "匹配域名關鍵字", "DOMAIN-REGEX": "匹配域名正則表達式", "GEOSITE": "匹配 Geosite 內的域名", "GEOIP": "匹配 IP 所屬國家代碼", "SRC-GEOIP": "匹配來源 IP 所屬國家代碼", "IP-ASN": "匹配 IP 所屬 ASN", "SRC-IP-ASN": "匹配來源 IP 所屬 ASN", "IP-CIDR": "匹配 IP 地址範圍", "IP-CIDR6": "匹配 IP 地址範圍", "SRC-IP-CIDR": "匹配來源 IP 地址範圍", "IP-SUFFIX": "匹配 IP 後綴範圍", "SRC-IP-SUFFIX": "匹配來源 IP 後綴範圍", "SRC-PORT": "匹配請求來源端口範圍", "DST-PORT": "匹配請求目標端口範圍", "IN-PORT": "匹配入站端口", "DSCP": "DSCP標記（僅限 TPROXY UDP 入站）", "PROCESS-NAME": "匹配進程名稱（Android 包名）", "PROCESS-PATH": "匹配完整進程路徑", "PROCESS-NAME-REGEX": "正則匹配完整進程名稱（Android 包名）", "PROCESS-PATH-REGEX": "正則匹配完整進程路徑", "NETWORK": "匹配傳輸協議 (TCP/UDP)", "UID": "匹配 Linux USER ID", "IN-TYPE": "匹配入站類型", "IN-USER": "匹配入站用戶名", "IN-NAME": "匹配入站名稱", "SUB-RULE": "子規則", "RULE-SET": "匹配規則集", "AND": "邏輯和", "OR": "邏輯或", "NOT": "邏輯非", "MATCH": "匹配所有請求", "DIRECT": "直連", "REJECT": "攔截請求", "REJECT-DROP": "拋棄請求", "PASS": "跳過此規則", "Edit Groups": "編輯代理組", "Group Type": "代理組類型", "select": "手動選擇代理", "url-test": "根據URL測試延遲選擇代理", "fallback": "不可用時切換到另一個代理", "load-balance": "根據負載均衡分配代理", "relay": "根據定義的代理鏈傳遞", "Group Name": "代理組組名", "Use Proxies": "引入代理", "Use Provider": "引入代理集合", "Health Check Url": "健康檢查測試地址", "Expected Status": "期望狀態碼", "Interval": "檢查間隔", "Lazy": "懶惰狀態", "Timeout": "超時時間", "Max Failed Times": "最大失敗次數", "Interface Name": "出站接口", "Routing Mark": "路由標記", "Include All": "引入所有出站代理、代理集合", "Include All Providers": "引入所有代理集合", "Include All Proxies": "引入所有出站代理", "Exclude Filter": "排除節點", "Exclude Type": "排除節點類型", "Disable UDP": "禁用 UDP", "Hidden": "隱藏代理組", "Group Name Required": "代理組名稱不能為空", "Group Name Already Exists": "代理組名稱已存在", "Extend Config": "擴展覆寫配置", "Extend Script": "擴展腳本", "Type": "類型", "Name": "名稱", "Descriptions": "描述", "Subscription URL": "訂閱鏈接", "Update Interval": "更新間隔", "Choose File": "選擇文件", "Use System Proxy": "使用系統代理更新", "Use Clash Proxy": "使用內核代理更新", "Refresh": "刷新", "Home": "首頁", "Select": "使用", "Edit Info": "編輯信息", "Edit File": "編輯文件", "Open File": "打開文件", "Update": "更新", "Confirm deletion": "確認刪除", "This operation is not reversible": "此操作不可逆", "Script Console": "腳本控制台輸出", "Connections": "連接", "Table View": "表格視圖", "List View": "列表視圖", "Close All": "關閉全部", "Upload": "上傳", "Download": "下載", "Download Speed": "下載速度", "Upload Speed": "上傳速度", "Host": "主機", "Downloaded": "下載量", "Uploaded": "上傳量", "DL Speed": "下載速度", "UL Speed": "上傳速度", "Active Connections": "活躍連接", "Chains": "鏈路", "Rule": "規則", "Process": "進程", "Time": "連接時間", "Source": "源地址", "Destination": "目標地址", "DestinationPort": "目標端口", "Close Connection": "關閉連接", "Rules": "規則", "Rule Provider": "規則集合", "Logs": "日誌", "Pause": "暫停", "Resume": "繼續", "Clear": "清除", "Test": "測試", "Test All": "測試全部", "Testing...": "測試中...", "Create Test": "新建測試", "Edit Test": "編輯測試", "Icon": "圖標", "Test URL": "測試地址", "Settings": "設置", "System Setting": "系統設置", "Tun Mode": "虛擬網卡模式", "TUN requires Service Mode or Admin Mode": "TUN 模式需要安裝服務或管理員模式", "Install Service": "安裝服務", "Reset to Default": "重置為默認值", "Tun Mode Info": "TUN（虛擬網卡）模式接管系統所有流量，啟用時無須打開系統代理", "System Proxy Enabled": "系統代理已啟用，您的應用將通過代理訪問網絡", "System Proxy Disabled": "系統代理已關閉，建議大多數用戶打開此選項", "TUN Mode Enabled": "TUN 模式已啟用，應用將通過虛擬網卡訪問網絡", "TUN Mode Disabled": "TUN 模式已關閉，適用於特殊應用", "TUN Mode Service Required": "TUN模式需要服務模式，請先安裝服務", "TUN Mode Intercept Info": "TUN模式可以接管所有應用流量，適用於特殊不遵循系統代理設置的應用", "Stack": "TUN 模式堆棧", "Device": "TUN 網卡名稱", "Auto Route": "自動設置全局路由", "Strict Route": "嚴格路由", "Auto Detect Interface": "自動選擇流量出口接口", "DNS Hijack": "DNS 劫持", "MTU": "最大傳輸單元", "Service Mode": "服務模式", "active": "已激活", "unknown": "未知", "Install": "安裝", "Uninstall": "卸載", "System Proxy": "系統代理", "System Proxy Info": "修改操作系統的代理設置，如果開啟失敗，可手動修改操作系統的代理設置", "System Proxy Setting": "系統代理設置", "Current System Proxy": "當前系統代理", "Enable status": "開啟狀態：", "Enabled": "已啟用", "Disabled": "未啟用", "Server Addr": "服務地址：", "Proxy Host": "代理主機", "Invalid Proxy Host Format": "代理主機格式無效", "Not available": "不可用", "Proxy Guard": "系統代理守衛", "Proxy Guard Info": "開啟以防止其他軟件修改操作系統的代理設置", "Guard Duration": "代理守衛間隔", "Always use Default Bypass": "始終使用默認繞過", "Proxy Bypass": "代理繞過設置：", "Bypass": "當前繞過：", "Use PAC Mode": "使用 PAC 模式", "PAC Script Content": "PAC 腳本內容", "PAC URL": "PAC 地址：", "Auto Launch": "開機自啟", "Administrator mode may not support auto launch": "管理員模式可能不支持開機自啟", "Silent Start": "靜默啟動", "Silent Start Info": "程序啟動時以後台模式運行，不顯示程序面板", "TG Channel": "Telegram 頻道", "Manual": "使用手冊", "Github Repo": "GitHub 項目地址", "Clash Setting": "Clash 設置", "Allow Lan": "局域網連接", "Network Interface": "網絡接口", "Ip Address": "IP 地址", "Mac Address": "MAC 地址", "IPv6": "IPv6", "Unified Delay": "統一延遲", "Unified Delay Info": "開啟統一延遲時，會進行兩次延遲測試，以消除連接握手等帶來的不同類型節點的延遲差異", "Log Level": "日誌等級", "Log Level Info": "僅對日誌目錄 Service 文件夾下的內核日誌文件生效", "Port Configuration": "端口設置", "Random Port": "隨機端口", "Mixed Port": "混合代理端口", "Socks Port": "SOCKS 代理端口", "Http Port": "HTTP(S) 代理端口", "Redir Port": "Redir 透明代理端口", "Tproxy Port": "TPROXY 透明代理端口", "External": "外部控制", "External Controller": "外部控制器監聽地址", "Core Secret": "API 訪問密鑰", "Recommended": "建議設置", "Open URL": "打開鏈接", "Replace host, port, secret with %host, %port, %secret": "使用 %host, %port, %secret 表示 主機, 端口, 訪問密鑰", "Support %host, %port, %secret": "支持 %host, %port, %secret", "Clash Core": "Clash 內核", "Upgrade": "升級內核", "Restart": "重啟內核", "Release Version": "正式版", "Alpha Version": "預覽版", "Please enter your root password": "請輸入您的 root 密碼", "Open UWP tool": "UWP 工具", "Open UWP tool Info": "Windows 8 開始限制 UWP 應用（如微軟商店）直接訪問本地主機的網絡服務，使用此工具可繞過該限制", "Update GeoData": "更新 GeoData", "Verge Basic Setting": "Verge 基礎設置", "Verge Advanced Setting": "Verge 高級設置", "Language": "語言設置", "Theme Mode": "主題模式", "theme.light": "淺色", "theme.dark": "深色", "theme.system": "系統", "Tray Click Event": "托盤點擊事件", "Show Main Window": "顯示主窗口", "Show Tray Menu": "顯示托盤菜單", "Copy Env Type": "複製環境變量類型", "Copy Success": "複製成功", "Start Page": "啟動頁面", "Startup Script": "啟動腳本", "Browse": "瀏覽", "Theme Setting": "主題設置", "Primary Color": "主要顏色", "Secondary Color": "次要顏色", "Primary Text": "文本主要顏色", "Secondary Text": "文本次要顏色", "Info Color": "信息顏色", "Warning Color": "警告顏色", "Error Color": "錯誤顏色", "Success Color": "成功顏色", "Font Family": "字體系列", "CSS Injection": "CSS 注入", "Layout Setting": "界面設置", "Traffic Graph": "流量圖顯", "Memory Usage": "內核佔用", "Memory Cleanup": "點擊清理內存", "Proxy Group Icon": "代理組圖標", "Nav Icon": "導航欄圖標", "Monochrome": "單色圖標", "Colorful": "彩色圖標", "Tray Icon": "托盤圖標", "Common Tray Icon": "常規托盤圖標", "System Proxy Tray Icon": "系統代理托盤圖標", "Tun Tray Icon": "TUN 模式托盤圖標", "Miscellaneous": "雜項設置", "App Log Level": "應用日誌等級", "Auto Close Connections": "自動關閉連接", "Auto Close Connections Info": "當代理組選中節點或代理模式變動時，關閉已建立的連接", "Auto Check Update": "自動檢查更新", "Enable Builtin Enhanced": "內置增強功能", "Enable Builtin Enhanced Info": "配置文件的兼容性處理", "Proxy Layout Columns": "代理頁布局列數", "Auto Columns": "自動列數", "Auto Log Clean": "自動清理日誌", "Never Clean": "不清理", "Retain _n Days": "保留 {{n}} 天", "Default Latency Test": "默認測試鏈接", "Default Latency Test Info": "僅用於 HTTP 客戶端請求測試，不會對配置文件產生影響", "Default Latency Timeout": "測試超時時間", "Hotkey Setting": "熱鍵設置", "Enable Global Hotkey": "啟用全局熱鍵", "open_or_close_dashboard": "打開/關閉面板", "clash_mode_rule": "規則模式", "clash_mode_global": "全局模式", "clash_mode_direct": "直連模式", "toggle_system_proxy": "打開/關閉系統代理", "toggle_tun_mode": "打開/關閉 TUN 模式", "entry_lightweight_mode": "進入輕量模式", "Backup Setting": "備份設置", "Backup Setting Info": "支持 WebDAV 備份配置文件", "Runtime Config": "當前配置", "Open Conf Dir": "配置目錄", "Open Conf Dir Info": "如果軟件運行異常，!備份!並刪除此文件夾下的所有文件，重啟軟件", "Open Core Dir": "內核目錄", "Open Logs Dir": "日誌目錄", "Check for Updates": "檢查更新", "Go to Release Page": "前往發佈頁", "Portable Updater Error": "便攜版不支持應用內更新，請手動下載替換", "Break Change Update Error": "此版本為重大更新，不支持應用內更新，請卸載後手動下載安裝", "Open Dev Tools": "開發者工具", "Export Diagnostic Info": "導出診斷信息", "Exit": "退出", "Verge Version": "Verge <PERSON>", "ReadOnly": "只讀", "ReadOnlyMessage": "無法在只讀模式下編輯", "Filter": "過濾節點", "Filter conditions": "過濾條件", "Match Case": "區分大小寫", "Match Whole Word": "全字匹配", "Use Regular Expression": "使用正則表達式", "Profile Imported Successfully": "導入訂閱成功", "Profile Switched": "訂閱已切換", "Profile Reactivated": "訂閱已激活", "Only YAML Files Supported": "僅支持 YAML 文件", "Settings Applied": "設置已應用", "Stopping Core...": "內核停止中...", "Restarting Core...": "內核重啟中...", "Installing Service...": "安裝服務中...", "Uninstall Service": "解除安裝服務", "Service Installed Successfully": "已成功安裝服務", "Service is ready and core restarted": "服務已就緒，內核已重啟", "Core restarted. Service is now available.": "內核已重啟，服務已就緒", "Service was ready, but core restart might have issues or service became unavailable. Please check.": "服務已就緒，但內核重啟可能存在問題或服務已不可用。請檢查。", "Service installation or core restart encountered issues. Service might not be available. Please check system logs.": "服務安裝或內核重啟遇到問題。服務可能不可用。請檢查系統日誌。", "Uninstalling Service...": "服務卸載中...", "Waiting for service to be ready...": "等待服務就緒...", "Service Uninstalled Successfully": "已成功卸載服務", "Proxy Daemon Duration Cannot be Less than 1 Second": "代理守護間隔時間不得低於 1 秒", "Invalid Bypass Format": "無效的代理繞過格式", "Core Version Updated": "內核版本已更新", "Clash Core Restarted": "已重啟 Clash 內核", "GeoData Updated": "已更新 GeoData", "Currently on the Latest Version": "當前已是最新版本", "Import Subscription Successful": "導入訂閱成功", "WebDAV Server URL": "WebDAV 服務器地址 http(s)://", "Username": "用戶名", "Password": "密碼", "Backup": "備份", "Filename": "文件名稱", "Actions": "操作", "Restore": "恢復", "No Backups": "暫無備份", "WebDAV URL Required": "WebDAV 服務器地址不能為空", "Invalid WebDAV URL": "無效的 WebDAV 服務器地址格式", "Username Required": "用戶名不能為空", "Password Required": "密碼不能為空", "WebDAV Config Saved": "WebDAV 配置保存成功", "WebDAV Config Save Failed": "保存 WebDAV 配置失敗: {{error}}", "Backup Created": "備份創建成功", "Backup Failed": "備份失敗: {{error}}", "Delete Backup": "刪除備份", "Restore Backup": "恢復備份", "Backup Time": "備份時間", "Restore Success, App will restart in 1s": "恢復成功，應用將在 1 秒後重啟", "Failed to fetch backup files": "獲取備份文件失敗", "Profile": "配置", "Web UI": "網頁界面", "Dashboard": "儀表板", "Restart App": "重啟應用", "Restart Clash Core": "重啟 Clash 核心", "TUN Mode": "TUN 模式", "Copy Env": "複製環境變量", "Conf Dir": "配置目錄", "Core Dir": "核心目錄", "Logs Dir": "日誌目錄", "Open Dir": "打開目錄", "More": "更多", "Rule Mode": "規則模式", "Global Mode": "全局模式", "Direct Mode": "直連模式", "Enable Tray Speed": "啟用托盤速率", "Enable Tray Icon": "啟用托盤圖標", "LightWeight Mode": "輕量模式", "LightWeight Mode Info": "關閉GUI界面，僅保留內核運行", "LightWeight Mode Settings": "輕量模式設置", "Enter LightWeight Mode Now": "立即進入輕量模式", "Auto Enter LightWeight Mode": "自動進入輕量模式", "Auto Enter LightWeight Mode Info": "啟用後，將在窗口關閉一段時間後自動激活輕量模式", "Auto Enter LightWeight Mode Delay": "自動進入輕量模式延遲", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "關閉窗口後，輕量模式將在 {{n}} 分鐘後自動激活", "Config Validation Failed": "訂閱配置校驗失敗，請檢查訂閱配置文件，變更已撤銷，錯誤詳情：", "Boot Config Validation Failed": "啟動訂閱配置校驗失敗，已使用默認配置啟動；請檢查訂閱配置文件，錯誤詳情：", "Core Change Config Validation Failed": "切換內核時配置校驗失敗，已使用默認配置啟動；請檢查訂閱配置文件，錯誤詳情：", "Config Validation Process Terminated": "驗證進程被終止", "Script Syntax Error": "腳本語法錯誤，變更已撤銷", "Script Missing Main": "腳本錯誤，變更已撤銷", "File Not Found": "文件丟失，變更已撤銷", "Script File Error": "腳本文件錯誤，變更已撤銷", "Core Changed Successfully": "內核切換成功", "Failed to Change Core": "無法切換內核", "YAML Syntax Error": "YAML語法錯誤，變更已撤銷", "YAML Read Error": "YAML讀取錯誤，變更已撤銷", "YAML Mapping Error": "YAML映射錯誤，變更已撤銷", "YAML Key Error": "YAML鍵錯誤，變更已撤銷", "YAML Error": "YAML錯誤，變更已撤銷", "Merge File Syntax Error": "覆寫文件語法錯誤，變更已撤銷", "Merge File Mapping Error": "覆寫文件映射錯誤，變更已撤銷", "Merge File Key Error": "覆寫文件鍵錯誤，變更已撤銷", "Merge File Error": "覆寫文件錯誤，變更已撤銷", "Service Administrator Prompt": "Clash Verge 需要管理員權限安裝系統服務", "DNS Settings": "DNS 設置", "DNS settings saved": "DNS 設置已保存", "DNS Overwrite": "DNS 覆寫", "DNS Settings Warning": "如果你不清楚這裡的設置請不要修改，並保持 DNS 覆寫開啟", "Enable DNS": "啟用 DNS", "DNS Listen": "DNS 監聽地址", "Enhanced Mode": "增強模式", "Fake IP Range": "Fake IP 範圍", "Fake IP Filter Mode": "Fake IP 過濾模式", "Enable IPv6 DNS resolution": "啟用 IPv6 DNS 解析", "Prefer H3": "優先使用 HTTP/3", "DNS DOH使用HTTP/3": "DNS DOH 使用 HTTP/3 協議", "Respect Rules": "遵循路由規則", "DNS connections follow routing rules": "DNS 連接遵循路由規則", "Use Hosts": "使用 Hosts", "Enable to resolve hosts through hosts file": "啟用通過 hosts 文件解析域名", "Use System Hosts": "使用系統 Hosts", "Enable to resolve hosts through system hosts file": "啟用通過系統 hosts 文件解析域名", "Direct Nameserver Follow Policy": "直連域名服務器遵循策略", "Whether to follow nameserver policy": "是否遵循 nameserver-policy 设置", "Default Nameserver": "默認域名服務器", "Default DNS servers used to resolve DNS servers": "用於解析 DNS 服務器的默認 DNS 服務器", "Nameserver": "域名服務器", "List of DNS servers": "DNS 服務器列表，用逗號分隔", "Fallback": "回退服務器", "List of fallback DNS servers": "回退 DNS 服務器列表，用逗號分隔", "Proxy Server Nameserver": "代理節點DNS", "Proxy Node Nameserver": "代理節點域名解析服務器，僅用於解析代理節點的域名，用逗號分隔", "Direct Nameserver": "直連域名服務器", "Direct outbound Nameserver": "直連出口域名解析服務器，支持 system 關鍵字，用逗號分隔", "Fake IP Filter": "Fake IP 過濾", "Domains that skip fake IP resolution": "跳過 Fake IP 解析的域名，用逗號分隔", "Nameserver Policy": "域名服務器策略", "Domain-specific DNS server": "特定域名的 DNS 服務器，多個服務器使用分號分隔，格式: domain=server1;server2", "Fallback Filter Settings": "回退過濾設置", "GeoIP Filtering": "GeoIP 過濾", "Enable GeoIP filtering for fallback": "啟用 GeoIP 回退過濾", "GeoIP Code": "GeoIP 國家代碼", "Fallback IP CIDR": "回退 IP CIDR", "IP CIDRs not using fallback servers": "不使用回退服務器的 IP CIDR，用逗號分隔", "Fallback Domain": "回退域名", "Domains using fallback servers": "使用回退服務器的域名，用逗號分隔", "Hosts Settings": "Hosts 設置", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "自定義域名到 IP 或域名的映射，用逗號分隔", "Home Settings": "首頁設置", "Profile Card": "訂閱卡", "Current Proxy Card": "當前代理卡", "Network Settings Card": "網絡設置卡", "Proxy Mode Card": "代理模式卡", "Traffic Stats Card": "流量統計卡", "Clash Info Cards": "Clash 信息卡", "System Info Cards": "系統信息卡", "Website Tests Card": "網站測試卡", "Traffic Stats": "流量統計", "Website Tests": "網站測試", "Clash Info": "Clash 信息", "Core Version": "內核版本", "System Proxy Address": "系統代理地址", "Uptime": "運行時間", "Rules Count": "規則數量", "System Info": "系統信息", "OS Info": "操作系統信息", "Running Mode": "運行模式", "Sidecar Mode": "用戶模式", "Administrator Mode": "管理員模式", "Last Check Update": "最後檢查更新", "Click to import subscription": "點擊導入訂閱", "Last Update failed": "上次更新失敗", "Next Up": "下次更新", "No schedule": "沒有計劃", "Unknown": "未知", "Auto update disabled": "自動更新已禁用", "Update subscription successfully": "訂閱更新成功", "Update failed, retrying with Clash proxy...": "訂閱更新失敗，嘗試使用 Clash 代理更新", "Update with Clash proxy successfully": "使用 Clash 代理更新成功", "Update failed even with Clash proxy": "使用 Clash 代理更新也失敗", "Profile creation failed, retrying with Clash proxy...": "訂閱創建失敗，嘗試使用 Clash 代理創建", "Profile creation succeeded with Clash proxy": "使用 Clash 代理創建訂閱成功", "Import failed, retrying with Clash proxy...": "訂閱導入失敗，嘗試使用 Clash 代理導入", "Profile Imported with Clash proxy": "使用 Clash 代理導入訂閱成功", "Import failed even with Clash proxy": "使用 Clash 代理導入訂閱也失敗", "Current Node": "當前節點", "No active proxy node": "暫無激活的代理節點", "Network Settings": "網絡設置", "Proxy Mode": "代理模式", "Group": "代理組", "Proxy": "節點", "IP Information Card": "IP信息卡", "IP Information": "IP信息", "Failed to get IP info": "獲取IP信息失敗", "ISP": "服務商", "ASN": "自治域", "ORG": "組織", "Location": "位置", "Timezone": "時區", "Auto refresh": "自動刷新", "Unlock Test": "解鎖測試", "Pending": "待檢測", "Yes": "支持", "No": "不支持", "Failed": "測試失敗", "Completed": "檢測完成", "Disallowed ISP": "不允許的 ISP", "Originals Only": "僅限原創", "Unsupported Country/Region": "不支持的國家/地區", "Controller address copied to clipboard": "API 端口已經複製到剪貼板", "Secret copied to clipboard": "API 密鑰已經複製到剪貼板", "Copy to clipboard": "點擊我複製", "Port Config": "端口設置", "Configuration saved successfully": "配置保存完成", "Enable one-click random API port and key. Click to randomize the port and key": "開啟一鍵隨機 API 端口和密鑰，點進去就可以隨機端口和密鑰"}