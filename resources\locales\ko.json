{"millis": "밀리초", "seconds": "초", "mins": "분", "Back": "뒤로", "Close": "닫기", "Cancel": "취소", "Confirm": "확인", "Maximize": "최대화", "Minimize": "최소화", "Format document": "문서 포맷", "Empty": "비어있음", "New": "새로 만들기", "Edit": "편집", "Save": "저장", "Delete": "삭제", "Enable": "활성화", "Disable": "비활성화", "Label-Home": "홈", "Label-Proxies": "프록시", "Label-Profiles": "프로필", "Label-Connections": "연결", "Label-Rules": "규칙", "Label-Logs": "로그", "Label-Unlock": "테스트", "Label-Settings": "설정", "Proxies": "프록시", "Proxy Groups": "프록시 그룹", "Proxy Provider": "프록시 제공자", "Proxy Count": "프록시 개수", "Update All": "모두 업데이트", "Update At": "업데이트 시간", "rule": "규칙", "global": "전역", "direct": "직접", "script": "스크립트", "locate": "로케이트", "Delay check": "지연 확인", "Sort by default": "기본값으로 정렬", "Sort by delay": "지연시간으로 정렬", "Sort by name": "이름으로 정렬", "Delay check URL": "지연 확인 URL", "Delay check to cancel fixed": "고정 취소를 위한 지연 확인", "Proxy basic": "프록시 기본", "Proxy detail": "프록시 상세", "Profiles": "프로필", "Update All Profiles": "모든 프로필 업데이트", "View Runtime Config": "런타임 설정 보기", "Reactivate Profiles": "프로필 재활성화", "Paste": "붙여넣기", "Profile URL": "프로필 URL", "Import": "가져오기", "From": "출처", "Update Time": "업데이트 시간", "Used / Total": "사용됨 / 전체", "Expire Time": "만료 시간", "Create Profile": "프로필 생성", "Edit Profile": "프로필 편집", "Edit Proxies": "프록시 편집", "Use newlines for multiple uri": "여러 URI의 경우 줄바꿈 사용(Base64 인코딩 지원)", "Edit Rules": "규칙 편집", "Rule Type": "규칙 유형", "Rule Content": "규칙 내용", "Proxy Policy": "프록시 정책", "No Resolve": "해석 안함", "Prepend Rule": "규칙 앞에 추가", "Append Rule": "규칙 뒤에 추가", "Prepend Group": "그룹 앞에 추가", "Append Group": "그룹 뒤에 추가", "Prepend Proxy": "프록시 앞에 추가", "Append Proxy": "프록시 뒤에 추가", "Rule Condition Required": "규칙 조건 필요", "Invalid Rule": "잘못된 규칙", "Advanced": "고급", "Visualization": "시각화", "DOMAIN": "전체 도메인 이름과 일치", "DOMAIN-SUFFIX": "도메인 접미사와 일치", "DOMAIN-KEYWORD": "도메인 키워드와 일치", "DOMAIN-REGEX": "정규 표현식을 사용한 도메인 일치", "GEOSITE": "Geosite 내의 도메인과 일치", "GEOIP": "IP 주소의 국가 코드와 일치", "SRC-GEOIP": "소스 IP 주소의 국가 코드와 일치", "IP-ASN": "IP 주소의 ASN과 일치", "SRC-IP-ASN": "소스 IP 주소의 ASN과 일치", "IP-CIDR": "IP 주소 범위와 일치", "IP-CIDR6": "IPv6 주소 범위와 일치", "SRC-IP-CIDR": "소스 IP 주소 범위와 일치", "IP-SUFFIX": "IP 주소 접미사 범위와 일치", "SRC-IP-SUFFIX": "소스 IP 주소 접미사 범위와 일치", "SRC-PORT": "소스 포트 범위와 일치", "DST-PORT": "대상 포트 범위와 일치", "IN-PORT": "인바운드 포트와 일치", "DSCP": "DSCP 마킹(tproxy UDP 인바운드만 해당)", "PROCESS-NAME": "프로세스 이름과 일치(안드로이드 패키지 이름)", "PROCESS-PATH": "전체 프로세스 경로와 일치", "PROCESS-NAME-REGEX": "정규 표현식을 사용한 전체 프로세스 이름 일치(안드로이드 패키지 이름)", "PROCESS-PATH-REGEX": "정규 표현식을 사용한 전체 프로세스 경로 일치", "NETWORK": "전송 프로토콜과 일치(tcp/udp)", "UID": "Linux 사용자 ID와 일치", "IN-TYPE": "인바운드 유형과 일치", "IN-USER": "인바운드 사용자 이름과 일치", "IN-NAME": "인바운드 이름과 일치", "SUB-RULE": "하위 규칙", "RULE-SET": "규칙 세트와 일치", "AND": "논리 AND", "OR": "논리 OR", "NOT": "논리 NOT", "MATCH": "모든 요청과 일치", "DIRECT": "데이터가 직접 아웃바운드로 이동", "REJECT": "요청 차단", "REJECT-DROP": "요청 폐기", "PASS": "일치할 경우 이 규칙 건너뛰기", "Edit Groups": "프록시 그룹 편집", "Group Type": "그룹 유형", "select": "수동으로 프록시 선택", "url-test": "URL 테스트 지연을 기준으로 프록시 선택", "fallback": "오류 발생 시 다른 프록시로 전환", "load-balance": "부하 분산에 따라 프록시 분배", "relay": "정의된 프록시 체인을 통과", "Group Name": "그룹 이름", "Use Proxies": "프록시 사용", "Use Provider": "제공자 사용", "Health Check Url": "상태 확인 URL", "Expected Status": "예상 상태", "Interval": "간격", "Lazy": "지연 로딩", "Timeout": "타임아웃", "Max Failed Times": "최대 실패 횟수", "Interface Name": "인터페이스 이름", "Routing Mark": "라우팅 마크", "Include All": "모든 프록시 및 제공자 포함", "Include All Providers": "모든 제공자 포함", "Include All Proxies": "모든 프록시 포함", "Exclude Filter": "제외 필터", "Exclude Type": "제외 유형", "Disable UDP": "UDP 비활성화", "Hidden": "숨김", "Group Name Required": "그룹 이름 필수", "Group Name Already Exists": "그룹 이름이 이미 존재함", "Extend Config": "설정 확장", "Extend Script": "스크립트 확장", "Global Merge": "전역 설정 확장", "Global Script": "전역 스크립트 확장", "Type": "유형", "Name": "이름", "Descriptions": "설명", "Subscription URL": "구독 URL", "Update Interval": "업데이트 간격", "Choose File": "파일 선택", "Use System Proxy": "시스템 프록시 사용", "Use Clash Proxy": "Clash 프록시 사용", "Accept Invalid Certs (Danger)": "잘못된 인증서 허용(위험)", "Refresh": "새로고침", "Home": "홈", "Select": "선택", "Edit Info": "정보 편집", "Edit File": "파일 편집", "Open File": "파일 열기", "Update": "업데이트", "Update(Proxy)": "업데이트(프록시)", "Confirm deletion": "삭제 확인", "This operation is not reversible": "이 작업은 되돌릴 수 없습니다", "Script Console": "스크립트 콘솔", "To Top": "맨 위로", "To End": "끝으로", "Connections": "연결", "Table View": "테이블 보기", "List View": "목록 보기", "Close All": "모두 닫기", "Upload": "업로드", "Download": "다운로드", "Download Speed": "다운로드 속도", "Upload Speed": "업로드 속도", "Host": "호스트", "Downloaded": "다운로드됨", "Uploaded": "업로드됨", "DL Speed": "다운로드 속도", "UL Speed": "업로드 속도", "Active Connections": "활성 연결", "Chains": "체인", "Rule": "규칙", "Process": "프로세스", "Time": "시간", "Source": "소스", "Destination": "목적지", "DestinationPort": "목적지 포트", "Close Connection": "연결 닫기", "Rules": "규칙", "Rule Provider": "규칙 제공자", "Logs": "로그", "Pause": "일시 정지", "Resume": "재개", "Clear": "지우기", "Test": "테스트", "Test All": "모두 테스트", "Testing...": "테스트 중...", "Create Test": "테스트 생성", "Edit Test": "테스트 편집", "Icon": "아이콘", "Test URL": "테스트 URL", "Timeout (ms)": "타임아웃 (ms)", "Expected": "예상됨", "URL": "URL", "Method": "메소드", "Failed": "실패", "Succeed": "성공", "Settings": "설정", "Core Config": "코어 설정", "Clash Setting": "Clash 설정", "Verge Setting": "Verge 설정", "System Setting": "시스템 설정", "Appearance": "외관", "Experimental Features": "실험적 기능", "Others": "기타", "Mixed Port": "혼합 포트", "Allow LAN": "LAN 허용", "IPv6": "IPv6", "Log Level": "로그 레벨", "Core Type": "코어 유형", "General": "일반", "Mode": "모드", "Tun Mode": "<PERSON>n 모드", "Transparent Proxy": "투명 프록시", "Specify YAML": "YAML 지정", "Status": "상태", "Memory Usage": "메모리 사용량", "Stack": "스택", "Network": "네트워크", "MTU": "MTU", "Auto Route": "자동 라우팅", "Auto Detect Interface": "인터페이스 자동 감지", "Endpoint Independent Nat": "엔드포인트 독립 NAT", "Include Reserved": "예약된 IP 포함", "Enable Default DNS Hijack": "기본 DNS 하이재킹 활성화", "TCP Fast Open": "TCP 빠른 열기", "Silent Start": "자동 시작", "TcpConcurrent": "TCP 동시성", "Service Mode": "서비스 모드", "System Proxy": "시스템 프록시", "Start With System": "시스템과 함께 시작", "Set System Proxy": "시스템 프록시 설정", "Set as System Proxy": "시스템 프록시로 설정", "System Proxy Status": "시스템 프록시 상태", "Start Option": "시작 옵션", "Start Core on Start": "시작 시 코어 시작", "Start Core with System": "시스템과 함께 코어 시작", "Start Core with System Proxy": "시스템 프록시와 함께 코어 시작", "Start Core with Tun": "Tun과 함께 코어 시작", "Silent Start Option": "자동 시작 옵션", "Hidden Window on Start": "시작 시 창 숨기기", "Log Notice": "로그 알림", "Warning": "경고", "Error": "오류", "Verge Basic Setting": "Verge 기본 설정", "Language": "언어", "Theme Mode": "테마 모드", "Tray Click Event": "트레이 클릭 이벤트", "Show Main Window": "메인 창 표시", "Show Tray Menu": "트레이 메뉴 표시", "Open Config Folder": "설정 폴더 열기", "Open Dashboard": "대시보드 열기", "Hotkey Setting": "단축키 설정", "Misc Setting": "기타 설정", "Layout Setting": "레이아웃 설정", "Update Setting": "업데이트 설정", "Enable Hotkeys": "단축키 활성화", "System Hotkey": "시스템 단축키", "Hotkey Enable": "단축키 활성화", "Require Clash Core Running": "Clash 코어 실행 필요", "Copy Env Type": "환경 유형 복사", "Start Page": "시작 페이지", "Startup Script": "시작 스크립트", "Icon Group Type": "아이콘 그룹 유형", "Always": "항상", "On Update": "업데이트 시", "By Traffic": "트래픽별", "Web UI List": "웹 UI 목록", "Installed Web UI": "설치된 웹 UI", "Built-in Web UI": "내장 웹 UI", "Current Config": "현재 설정", "System Config": "시스템 설정", "Port": "포트", "WebUI Current Port": "웹 UI 현재 포트", "Theme": "테마", "Light": "라이트", "Dark": "다크", "Auto": "자동", "System": "시스템", "Proxy Item Width": "프록시 항목 너비", "Proxy Item Height": "프록시 항목 높이", "Compact Mode": "압축 모드", "Git Proxy": "Git 프록시", "Enable API": "API 활성화", "Enable Lan": "LAN 활성화", "Select a config file": "설정 파일 선택", "Open Config Dir": "설정 디렉토리 열기", "System Proxy Permission": "시스템 프록시 권한", "System Stack Type": "시스템 스택 유형", "Undefined stack": "정의되지 않은 스택", "Auto Start": "자동 시작", "Mixin": "혼합", "Set as System Auto Proxy": "시스템 자동 프록시로 설정", "System Auto Proxy Status": "시스템 자동 프록시 상태", "Authorization for requests coming through HTTP Proxy (e.g. local connections)": "HTTP 프록시를 통한 요청에 대한 인증 (예: 로컬 연결)", "Username": "사용자 이름", "Password": "비밀번호", "Auth Proxy": "인증 프록시", "Geox User": "Geox 사용자", "Geox Password": "Geox 비밀번호", "Log File": "로그 파일", "Enable Clash.Meta Logs": "Clash.Meta 로그 활성화", "Verge Log": "Verge 로그", "Enable Verge Logs": "Verge 로그 활성화", "Traffic Graph": "트래픽 그래프", "Profile Token": "프로필 토큰", "Profile User Agent": "프로필 사용자 에이전트", "The User Agent to use when refreshing a subscription profile.": "구독 프로필을 새로 고칠 때 사용할 사용자 에이전트입니다.", "Profile Format": "프로필 포맷", "The expected content type to send in the `Accept` header when refreshing a subscription profile.": "구독 프로필을 새로 고칠 때 `Accept` 헤더에 보낼 예상 컨텐츠 타입입니다.", "Theme Color": "테마 색상", "Primary Color": "기본 색상", "Customize primary color": "기본 색상 사용자 정의", "Danger Zone": "위험 영역", "Reset Verge Theme": "Verge 테마 재설정", "Inject CSS": "CSS 주입", "Inject a custom CSS content into the GUI": "사용자 정의 CSS 내용을 GUI에 주입", "Inject HTML": "HTML 주입", "Inject a custom HTML content into the GUI (appended in body)": "사용자 정의 HTML 내용을 GUI에 주입 (본문에 추가)", "Capture": "캡처", "Color Scheme": "색상 구성표", "Default": "기본값", "Pink": "분홍색", "Red": "빨간색", "Yellow": "노란색", "Green": "녹색", "Cyan": "청록색", "Blue": "파란색", "Purple": "보라색", "Proxy Detail": "프록시 상세", "Address": "주소", "Filter": "필터", "Check Updates on Start": "시작 시 업데이트 확인", "For Alpha Version": "알파 버전용", "Latest Build Version": "최신 빌드 버전", "Check Updates": "업데이트 확인", "Proxy Setting": "프록시 설정", "WebDav Setting": "WebDav 설정", "WebDav Upload": "WebDav 업로드", "WebDav Download": "WebDav 다운로드", "Clean Cache": "캐시 정리", "Check Network": "네트워크 확인", "WebDav Status": "WebDav 상태", "WebDav URL": "WebDav URL", "WebDav Username": "WebDav 사용자 이름", "WebDav Password": "WebDav 비밀번호", "Update Interval(minute)": "업데이트 간격(분)", "Skip Cert Verify": "인증서 확인 건너뛰기", "Import Subscription Successful": "구독 가져오기 성공", "Update with Clash proxy successfully": "Clash 프록시로 업데이트 성공", "Update failed, retrying with Clash proxy...": "업데이트 실패, Clash 프록시로 재시도 중...", "Update failed even with Clash proxy": "Clash 프록시로도 업데이트 실패", "Boot Config Validation Failed": "부팅 설정 검증 실패", "Core Change Config Validation Failed": "코어 변경 설정 검증 실패", "Config Validation Failed": "설정 검증 실패", "Config Validation Process Terminated": "설정 검증 프로세스 종료됨", "Script File Error": "스크립트 파일 오류", "Script Syntax Error": "스크립트 구문 오류", "Script Missing Main": "스크립트 메인 없음", "File Not Found": "파일을 찾을 수 없음", "YAML Syntax Error": "YAML 구문 오류", "YAML Read Error": "YAML 읽기 오류", "YAML Mapping Error": "YAML 매핑 오류", "YAML Key Error": "YAML 키 오류", "YAML Error": "YAML 오류", "Merge File Syntax Error": "병합 파일 구문 오류", "Merge File Mapping Error": "병합 파일 매핑 오류", "Merge File Key Error": "병합 파일 키 오류", "Merge File Error": "병합 파일 오류", "Core Changed Successfully": "코어 변경 성공", "Failed to Change Core": "코어 변경 실패", "Copy Success": "복사 성공", "Copy Failed": "복사 실패", "Cannot Import Empty Subscription URL": "빈 구독 URL을 가져올 수 없습니다", "Profile Already Exists": "프로필이 이미 존재합니다", "Input Subscription URL": "구독 URL 입력", "Create Profile Successful": "프로필 생성 성공", "Create Profile Failed": "프로필 생성 실패", "Patch Profile Successful": "프로필 패치 성공", "Patch Profile Failed": "프로필 패치 실패", "Delete Profile Successful": "프로필 삭제 성공", "Delete Profile Failed": "프로필 삭제 실패", "Select Active Profile Successful": "활성 프로필 선택 성공", "Select Active Profile Failed": "활성 프로필 선택 실패", "View Profile-Runtime": "프로필-런타임 보기", "View Profile-Content": "프로필-내용 보기", "View Profile-Original": "프로필-원본 보기", "View Profile-Script": "프로필-스크립트 보기", "View Profile-Merge": "프로필-병합 보기", "Update Successful": "업데이트 성공", "Update Failed": "업데이트 실패"}