{"millis": "Millisekunden", "seconds": "Sekunden", "mins": "Minuten", "Back": "Zurück", "Close": "Schließen", "Cancel": "Abbrechen", "Confirm": "Bestätigen", "Maximize": "<PERSON><PERSON><PERSON>", "Minimize": "Minimieren", "Format document": "Dokument formatieren", "Empty": "<PERSON><PERSON>", "New": "<PERSON>eu", "Edit": "<PERSON><PERSON><PERSON>", "Save": "Speichern", "Delete": "Löschen", "Enable": "Aktivieren", "Disable": "Deaktivieren", "Label-Home": "Startseite", "Label-Proxies": "Proxy", "Label-Profiles": "Abonnement", "Label-Connections": "Verbindungen", "Label-Rules": "Regeln", "Label-Logs": "Protokolle", "Label-Unlock": "<PERSON><PERSON>", "Label-Settings": "Einstellungen", "Proxy Groups": "Proxy-Gruppen", "Proxy Provider": "Proxy-Sam<PERSON><PERSON>", "Proxy Count": "<PERSON><PERSON><PERSON> der Knoten", "Update All": "Alle aktualisieren", "Update At": "Aktualisiert am", "rule": "Regel", "global": "Global", "direct": "Direktverbindung", "script": "S<PERSON><PERSON><PERSON>", "locate": "Aktueller Knoten", "Delay check": "Latenztest", "Sort by default": "Standard Sortierung", "Sort by delay": "<PERSON>ch Latenz sortieren", "Sort by name": "Nach Name sortieren", "Delay check URL": "Latenztest-URL", "Delay check to cancel fixed": "Latenztest durchführen, um Fixierung aufzuheben", "Proxy basic": "Knotendetails ausblenden", "Proxy detail": "Knotendetails anzeigen", "Profiles": "Abonnement", "Update All Profiles": "Alle Abonnements aktualisieren", "View Runtime Config": "Laufzeit-Abonnement anzeigen", "Reactivate Profiles": "Abonnement erneut aktivieren", "Paste": "Einfügen", "Profile URL": "Abonnement-Datei-Link", "Import": "Importieren", "From": "<PERSON>", "Update Time": "Aktualisierungszeit", "Used / Total": "Verwendet / Gesamt", "Expire Time": "Ablaufzeit", "Create Profile": "Neue Konfiguration erstellen", "Edit Profile": "Konfiguration bearbeiten", "Edit Proxies": "Knoten bearbeiten", "Use newlines for multiple uri": "<PERSON><PERSON><PERSON> mehrere URI verwenden Sie Zeilenumbrüche (Base64-Codierung wird unterstützt)", "Edit Rules": "Regeln bearbeiten", "Rule Type": "Regeltyp", "Rule Content": "<PERSON>elinhalt", "Proxy Policy": "Proxy-Strategie", "No Resolve": "DNS-Auflösung überspringen", "Prepend Rule": "Vorherige Regel hinzufügen", "Append Rule": "Nachfolgende Regel hinzufügen", "Prepend Group": "Vorherige Proxy-Gruppe hinzufügen", "Append Group": "Nachfolgende Proxy-Gruppe hinzufügen", "Prepend Proxy": "Vorherigen Proxy-Knoten hinzufügen", "Append Proxy": "Nachfolgenden Proxy-Knoten hinzufügen", "Rule Condition Required": "Regelbedingung fehlt", "Invalid Rule": "Ungültige Regel", "Advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Visualization": "Visualisierung", "DOMAIN": "Vollständigen Domainnamen übereinstimmen", "DOMAIN-SUFFIX": "Domain-Suffix übereinstimmen", "DOMAIN-KEYWORD": "Domain-Schlüsselwort übereinstimmen", "DOMAIN-REGEX": "Domain-Regulärer Ausdruck übereinstimmen", "GEOSITE": "Domainnamen in Geosite übereinstimmen", "GEOIP": "IP-Ländercode übereinstimmen", "SRC-GEOIP": "Quell-IP-Ländercode übereinstimmen", "IP-ASN": "IP-ASN übereinstimmen", "SRC-IP-ASN": "Quell-IP-ASN übereinstimmen", "IP-CIDR": "IP-Adressbereich übereinstimmen", "IP-CIDR6": "IP-Adressbereich übereinstimmen", "SRC-IP-CIDR": "Quell-IP-Adressbereich übereinstimmen", "IP-SUFFIX": "IP-Suffix-Bereich übereinstimmen", "SRC-IP-SUFFIX": "Quell-IP-Suffix-Bereich übereinstimmen", "SRC-PORT": "Quellportbereich der Anfrage übereinstimmen", "DST-PORT": "Zielportbereich der Anfrage übereinstimmen", "IN-PORT": "Eingangsport übereinstimmen", "DSCP": "DSCP-Markierung (nur für TPROXY UDP-Eingang)", "PROCESS-NAME": "Prozessnamen übereinstimmen (Android-Paketname)", "PROCESS-PATH": "Vollständigen Prozesspfad übereinstimmen", "PROCESS-NAME-REGEX": "Regulärer Ausdruck für vollständigen Prozessnamen übereinstimmen (Android-Paketname)", "PROCESS-PATH-REGEX": "Regulärer Ausdruck für vollständigen Prozesspfad übereinstimmen", "NETWORK": "Übertragungsprotokoll übereinstimmen (TCP/UDP)", "UID": "Linux-USER-ID übereinstimmen", "IN-TYPE": "Eingangstyp übereinstimmen", "IN-USER": "Eingangsbenutzername übereinstimmen", "IN-NAME": "Eingangsname übereinstimmen", "SUB-RULE": "Unterregel", "RULE-SET": "Regelsatz übereinstimmen", "AND": "Logisches UND", "OR": "Logisches ODER", "NOT": "Logisches NICHT", "MATCH": "Alle Anfragen übereinstimmen", "DIRECT": "Direktverbindung", "REJECT": "<PERSON><PERSON><PERSON>", "REJECT-DROP": "Anfrage verwerfen", "PASS": "Diese Regel überspringen", "Edit Groups": "Proxy-Gruppen bearbeiten", "Group Type": "Proxy-Gruppentyp", "select": "Proxy manuell au<PERSON><PERSON><PERSON>en", "url-test": "Proxy basierend auf URL-Latenztest auswählen", "fallback": "Bei Nichtverfügbarkeit zu einem anderen Proxy wechseln", "load-balance": "Proxy basierend auf Lastverteilung zuweisen", "relay": "Basierend auf definiertem Proxy-<PERSON><PERSON> we<PERSON>n", "Group Name": "Proxy-Gruppenname", "Use Proxies": "Proxy einf<PERSON><PERSON>", "Use Provider": "Proxy-<PERSON><PERSON><PERSON> e<PERSON>", "Health Check Url": "URL für Gesundheitstest", "Expected Status": "Erwarteter Statuscode", "Interval": "Prüfintervall", "Lazy": "Lazy-Status", "Timeout": "Timeout", "Max Failed Times": "Maximale Anzahl fehlgeschlagener Versuche", "Interface Name": "Ausgangsschnittstelle", "Routing Mark": "Routierungsmarkierung", "Include All": "Alle Ausgangsproxy und Proxy-Sammlungen einführen", "Include All Providers": "Alle Proxy-Sammlungen einführen", "Include All Proxies": "Alle Ausgangsproxy einführen", "Exclude Filter": "Knoten ausschließen", "Exclude Type": "Typ der auszuschließenden Knoten", "Disable UDP": "UDP deaktivieren", "Hidden": "Proxy-Gruppe ausblenden", "Group Name Required": "Der Proxy-Gruppenname darf nicht leer sein", "Group Name Already Exists": "Der Proxy-Gruppenname existiert bereits", "Extend Config": "Erweiterte Überdeckungskonfiguration", "Extend Script": "Erweitertes Skript", "Type": "<PERSON><PERSON>", "Name": "Name", "Descriptions": "Beschreibung", "Subscription URL": "Abonnement-Link", "Update Interval": "Aktualisierungsintervall", "Choose File": "<PERSON>i ausw<PERSON>hlen", "Use System Proxy": "Systemproxy zur Aktualisierung verwenden", "Use Clash Proxy": "Kernel-Proxy zur Aktualisierung verwenden", "Refresh": "Aktualisieren", "Home": "Startseite", "Select": "Verwenden", "Edit Info": "<PERSON>en bearbeiten", "Edit File": "<PERSON><PERSON> bearbeiten", "Open File": "<PERSON><PERSON>", "Update": "Aktualisieren", "Confirm deletion": "Löschung bestätigen", "This operation is not reversible": "Diese Operation kann nicht rückgängig gemacht werden", "Script Console": "Skript-Konsole-Ausgabe", "Connections": "Verbindungen", "Table View": "Tabellenansicht", "List View": "Listenansicht", "Close All": "Alle schließen", "Upload": "Hochladen", "Download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Download Speed": "Download-Geschwindigkeit", "Upload Speed": "Upload-Geschwindigkeit", "Host": "Host", "Downloaded": "Her<PERSON><PERSON>gelade<PERSON>", "Uploaded": "Hochgeladen", "DL Speed": "Download-Geschwindigkeit", "UL Speed": "Upload-Geschwindigkeit", "Active Connections": "Aktive Verbindungen", "Chains": "<PERSON><PERSON>", "Rule": "Regel", "Process": "Prozess", "Time": "Verbindungszeit", "Source": "Quelladresse", "Destination": "Zieladresse", "DestinationPort": "Zielport", "Close Connection": "Verbindung schließen", "Rules": "Regeln", "Rule Provider": "Regelsammlung", "Logs": "Protokolle", "Pause": "Pausieren", "Resume": "Fortsetzen", "Clear": "Löschen", "Test": "<PERSON><PERSON>", "Test All": "Alle testen", "Testing...": "Wird getestet...", "Create Test": "Neuen Test erstellen", "Edit Test": "Test bearbeiten", "Icon": "Symbol", "Test URL": "Test-URL", "Settings": "Einstellungen", "System Setting": "Systemeinstellungen", "Tun Mode": "Virtual Network Interface-Modus", "TUN requires Service Mode or Admin Mode": "TUN-Modus erfordert Service-Modus oder Administrator-Modus", "Install Service": "Service installieren", "Reset to Default": "Auf Standardwerte zurücksetzen", "Tun Mode Info": "Der TUN-Modus (Virtual Network Interface) übernimmt den gesamten Systemverkehr. <PERSON><PERSON> dieser Modus aktiviert ist, muss der Systemproxy nicht geöffnet werden.", "System Proxy Enabled": "Der Systemproxy ist aktiviert. Ihre Anwendungen werden über den Proxy auf das Netzwerk zugreifen.", "System Proxy Disabled": "Der Systemproxy ist deaktiviert. Es wird empfohlen, diesen Eintrag für die meisten Benutzer zu aktivieren.", "TUN Mode Enabled": "Der TUN-Modus ist aktiviert. Die Anwendungen werden über die virtuelle Netzwerkschnittstelle auf das Netzwerk zugreifen.", "TUN Mode Disabled": "Der TUN-Modus ist deaktiviert. Dies ist für spezielle Anwendungen geeignet.", "TUN Mode Service Required": "Der TUN-Modus erfordert den Service-Modus. Bitte installieren Sie zuerst den Service.", "TUN Mode Intercept Info": "Der TUN-Modus kann den gesamten Anwendungsverkehr übernehmen und eignet sich für spezielle Anwendungen, die die Systemproxy-Einstellungen nicht befolgen.", "Stack": "TUN-Modus-Stack", "Device": "TUN-Netzwerkkartenname", "Auto Route": "Globale Routing automatisch einstellen", "Strict Route": "Strenges Routing", "Auto Detect Interface": "Netzwerkschnittstelle automatisch auswählen", "DNS Hijack": "DNS-Hijacking", "MTU": "Maximale Übertragungseinheit", "Service Mode": "Service-Modus", "active": "Aktiviert", "unknown": "Unbekannt", "Install": "Installieren", "Uninstall": "Deinstallieren", "System Proxy": "Systemproxy", "System Proxy Info": "Ändern Sie die Proxy-Einstellungen des Betriebssystems. Wenn die Aktivierung fehlschlägt, können Sie die Proxy-Einstellungen des Betriebssystems manuell ändern.", "System Proxy Setting": "Systemproxy-Einstellungen", "Current System Proxy": "Aktueller Systemproxy", "Enable status": "Aktivierungsstatus: ", "Enabled": "Aktiviert", "Disabled": "Deaktiviert", "Server Addr": "Serveradresse: ", "Proxy Host": "Proxy-Host", "Invalid Proxy Host Format": "Ungültiges Format für den Proxy-Host", "Not available": "Nicht verfügbar", "Proxy Guard": "Systemproxy-Schutz", "Proxy Guard Info": "Aktivieren Sie diese Option, um zu verhindern, dass andere Software die Proxy-Einstellungen des Betriebssystems ändert.", "Guard Duration": "Proxy-Schutz-Intervall", "Always use Default Bypass": "Immer die Standard-Umgehung verwenden", "Proxy Bypass": "Proxy-Umgehungseinstellungen: ", "Bypass": "Aktuelle Umgehung: ", "Use PAC Mode": "PAC-Modus verwenden", "PAC Script Content": "PAC-Skriptinhalt", "PAC URL": "PAC-Adresse: ", "Auto Launch": "<PERSON><PERSON> Start automatisch starten", "Administrator mode may not support auto launch": "Der Administrator-Modus unterstützt möglicherweise keine automatische Startfunktion.", "Silent Start": "Stillstart", "Silent Start Info": "<PERSON> Anwendung wird im Hintergrund gestartet, ohne dass das Programmfenster angezeigt wird.", "TG Channel": "Telegram-Kanal", "Manual": "Bedienungsanleitung", "Github Repo": "GitHub-Projektadresse", "Clash Setting": "Clash-Einstellungen", "Allow Lan": "Netzwerkverbindung im lokalen Netzwerk zulassen", "Network Interface": "Netzwerkschnittstelle", "Ip Address": "IP-Adresse", "Mac Address": "MAC-Adresse", "IPv6": "IPv6", "Unified Delay": "Einheitliche Latenz", "Unified Delay Info": "Wenn die einheitliche Latenz aktiviert ist, werden zwei Latenztests durchgeführt, um die Latenzunterschiede zwischen verschiedenen Knotentypen aufgrund von Verbindungsaufbau und anderen Faktoren zu eliminieren.", "Log Level": "Protokolliergrad", "Log Level Info": "Dies wirkt sich nur auf die Kernprotokolldateien im Verzeichnis Service im Protokollverzeichnis aus.", "Port Configuration": "Porteinstellungen", "Random Port": "Zufälliger Port", "Mixed Port": "Mischter Proxy-Port", "Socks Port": "SOCKS-Proxy-Port", "Http Port": "HTTP(S)-Proxy-Port", "Redir Port": "Redir-Transparenter Proxy-Port", "Tproxy Port": "TPROXY-Transparenter Proxy-Port", "External": "Externe Steuerung", "External Controller": "Adresse des externen Controllers", "Core Secret": "API-Zugangsschlüssel", "Recommended": "Empfohlene <PERSON>ung", "Open URL": "<PERSON>", "Replace host, port, secret with %host, %port, %secret": "Verwenden Sie %host, %port, %secret für Host, Port und Zugangsschlüssel", "Support %host, %port, %secret": "Unterstützt %host, %port, %secret", "Clash Core": "Clash-<PERSON>", "Upgrade": "Kern aktualisieren", "Restart": "<PERSON>", "Release Version": "Stabile Version", "Alpha Version": "Vorschauversion", "Please enter your root password": "Bitte geben Sie Ihr Root-Passwort ein.", "Open UWP tool": "UWP-<PERSON><PERSON>", "Open UWP tool Info": "Ab Windows 8 wird die direkte Netzwerkverbindung von UWP-Anwendungen (z. B. Microsoft Store) zu lokalen Hosts eingeschränkt. Mit diesem Tool können Sie diese Einschränkung umgehen.", "Update GeoData": "Geo-Daten aktualisieren", "Verge Basic Setting": "Verge-Grundeinstellungen", "Verge Advanced Setting": "Verge-Erweiterte Einstellungen", "Language": "Spracheinstellungen", "Theme Mode": "<PERSON>a", "Tray Click Event": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Show Main Window": "Hauptfenster anzeigen", "Show Tray Menu": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen", "Copy Env Type": "Umgebungsvariablentyp kopieren", "Copy Success": "<PERSON><PERSON><PERSON>", "Start Page": "Startseite", "Startup Script": "Startskript", "Browse": "Durchsuchen", "Theme Setting": "Thema-Einstellungen", "Primary Color": "Hauptfarbe", "Secondary Color": "Sekundärfarbe", "Primary Text": "Haupttextfarbe", "Secondary Text": "Sekundärtextfarbe", "Info Color": "Informationsfarbe", "Warning Color": "Warnfarbe", "Error Color": "Fehlerfarbe", "Success Color": "Erfolgsfarbe", "Font Family": "Schriftfamilie", "CSS Injection": "CSS-Einbindung", "Layout Setting": "Layout-Einstellungen", "Traffic Graph": "Verkehrsdiagramm", "Memory Usage": "Kern-Speichernutzung", "Memory Cleanup": "<PERSON><PERSON><PERSON> hier, um den Speicher zu bereinigen.", "Proxy Group Icon": "Proxy-Gruppen-Symbol", "Nav Icon": "Navigationsleiste-Symbol", "Monochrome": "Monochromes Symbol", "Colorful": "Farbiges Symbol", "Tray Icon": "Tray-Symbol", "Common Tray Icon": "Standard-Tray-Symbol", "System Proxy Tray Icon": "Systemproxy-Tray-Symbol", "Tun Tray Icon": "TUN-Modus-Tray-Symbol", "Miscellaneous": "Sonstige Einstellungen", "App Log Level": "Anwendungs-Protokolliergrad", "Auto Close Connections": "Verbindungen automatisch schließen", "Auto Close Connections Info": "Wenn der ausgewählte Knoten in der Proxy-Gruppe oder der Proxy-Modus geändert wird, werden die bestehenden Verbindungen geschlossen.", "Auto Check Update": "Automatisch auf Updates prüfen", "Enable Builtin Enhanced": "Eingebaute Verbesserungen aktivieren", "Enable Builtin Enhanced Info": "Kompatibilitätsbehandlung der Konfigurationsdatei", "Proxy Layout Columns": "Anzahl der Spalten im Proxy-Layout", "Auto Columns": "Automatische Anzahl der Spalten", "Auto Log Clean": "Protokolle automatisch bereinigen", "Never Clean": "<PERSON>e bere<PERSON>gen", "Retain _n Days": "{{n}} <PERSON><PERSON> behalten", "Default Latency Test": "Standard-Testlink", "Default Latency Test Info": "Dies wird nur für HTTP-Client-Anfragentests verwendet und hat keine Auswirkungen auf die Konfigurationsdatei.", "Default Latency Timeout": "Test-Timeout", "Hotkey Setting": "Tastenkombinationseinstellungen", "Enable Global Hotkey": "Globale Tastenkombinationen aktivieren", "open_or_close_dashboard": "Dashboard öffnen/schließen", "clash_mode_rule": "Regel-Modus", "clash_mode_global": "Globaler <PERSON>", "clash_mode_direct": "Direktverbindungs-Modus", "toggle_system_proxy": "Systemproxy ein/ausschalten", "toggle_tun_mode": "TUN-Modus ein/ausschalten", "entry_lightweight_mode": "Leichtgewichtigen Modus betreten", "Backup Setting": "Sicherungseinstellungen", "Backup Setting Info": "Unterstützt die Sicherung von Konfigurationsdateien über WebDAV", "Runtime Config": "Aktuelle Konfiguration", "Open Conf Dir": "Konfigurationsverzeichnis", "Open Conf Dir Info": "Wenn die Software fehlerhaft funktioniert, !sichern Sie! alle Dateien in diesem Verzeichnis, löschen Sie sie und starten Sie die Software neu.", "Open Core Dir": "Kernverzeichnis", "Open Logs Dir": "Protokollverzeichnis", "Check for Updates": "<PERSON>f Updates prüfen", "Go to Release Page": "Zur Veröffentlichungsseite gehen", "Portable Updater Error": "Die portable Version unterstützt keine In-App-Aktualisierung. Bitte laden Sie die Dateien manuell herunter und ersetzen Sie sie.", "Break Change Update Error": "Dies ist eine wichtige Aktualisierung. Die In-App-Aktualisierung wird nicht unterstützt. Bitte deinstallieren Sie die Software und laden Sie die neue Version manuell herunter und installieren Sie sie.", "Open Dev Tools": "Entwicklertools öffnen", "Export Diagnostic Info": "Diagnoseinformationen exportieren", "Exit": "<PERSON>den", "Verge Version": "Verge-Version", "ReadOnly": "Schreibgeschützt", "ReadOnlyMessage": "Bearbeitung im schreibgeschützten Modus nicht möglich", "Filter": "Knoten filtern", "Filter conditions": "Filterbedingungen", "Match Case": "Groß-/Kleinschreibung beachten", "Match Whole Word": "Ganzes Wort übereinstimmen", "Use Regular Expression": "Regulären Ausdruck verwenden", "Profile Imported Successfully": "Abonnement erfolgreich importiert", "Profile Switched": "Abonnement gewechselt", "Profile Reactivated": "Abonnement erneut aktiviert", "Only YAML Files Supported": "Nur YAML-<PERSON><PERSON> werden unterstützt", "Settings Applied": "Einstellungen angewendet", "Stopping Core...": "Kern wird gestoppt...", "Restarting Core...": "Kern wird neu gestartet...", "Installing Service...": "Service wird installiert...", "Uninstall Service": "<PERSON><PERSON> deinstall<PERSON>en", "Service Installed Successfully": "Service erfolgreich installiert", "Service is ready and core restarted": "Service ist bereit und Kern wurde neu gestartet", "Core restarted. Service is now available.": "Kern wurde neu gestartet. Service ist jetzt verfügbar", "Service was ready, but core restart might have issues or service became unavailable. Please check.": "Der Dienst war bereit, aber beim Neustart des Kerns könnten Probleme aufgetreten sein oder der Dienst ist möglicherweise nicht verfügbar. Bitte überprüfen Sie dies.", "Service installation or core restart encountered issues. Service might not be available. Please check system logs.": "Bei der Dienstinstallation oder dem Neustart des Kerns sind Probleme aufgetreten. Der Dienst ist möglicherweise nicht verfügbar. Bitte prüfen Sie die Systemprotokolle.", "Uninstalling Service...": "Service wird deinstalliert...", "Waiting for service to be ready...": "Auf Service-Bereitschaft gewartet...", "Service Uninstalled Successfully": "Service erfolgreich deinstalliert", "Proxy Daemon Duration Cannot be Less than 1 Second": "Das Intervall des Proxy-Daemons darf nicht weniger als 1 Sekunde betragen.", "Invalid Bypass Format": "Ungültiges Format für die Proxy-Umgehung", "Core Version Updated": "Kernversion wurde aktualisiert", "Clash Core Restarted": "<PERSON>lash<PERSON><PERSON> wurde neu gestartet", "GeoData Updated": "Geo-Daten wurden aktualisiert", "Currently on the Latest Version": "Sie verwenden bereits die neueste Version", "Import Subscription Successful": "Abonnement erfolgreich importiert", "WebDAV Server URL": "WebDAV-Serveradresse http(s)://", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Passwort", "Backup": "<PERSON><PERSON><PERSON>", "Filename": "Dateiname", "Actions": "Aktionen", "Restore": "Wiederherstellen", "No Backups": "<PERSON><PERSON> vorhanden", "WebDAV URL Required": "Die WebDAV-Serveradresse darf nicht leer sein", "Invalid WebDAV URL": "Ungültiges Format für die WebDAV-Serveradresse", "Username Required": "Der Benutzername darf nicht leer sein", "Password Required": "Das Passwort darf nicht leer sein", "WebDAV Config Saved": "WebDAV-Konfiguration erfolgreich gespeichert", "WebDAV Config Save Failed": "Speichern der WebDAV-Konfiguration fehlgeschlagen: {{error}}", "Backup Created": "Sicherung erfolgreich erstellt", "Backup Failed": "Sicherung fehlgeschlagen: {{error}}", "Delete Backup": "Sicherung löschen", "Restore Backup": "Sicherung wiederherstellen", "Backup Time": "Sicherungszeit", "Restore Success, App will restart in 1s": "Wiederherstellung erfolgreich. Die App wird in 1 Sekunde neu starten.", "Failed to fetch backup files": "Abrufen der Sicherungsdateien fehlgeschlagen", "Profile": "Konfiguration", "Web UI": "Web-Oberfläche", "Dashboard": "Dashboard", "Restart App": "App neu starten", "Restart Clash Core": "Clash-<PERSON> neu starten", "TUN Mode": "TUN-Modus", "Copy Env": "Umgebungsvariablen kopieren", "Conf Dir": "Konfigurationsverzeichnis", "Core Dir": "Kernverzeichnis", "Logs Dir": "Protokollverzeichnis", "Open Dir": "Verzeichnis öffnen", "More": "<PERSON><PERSON>", "Rule Mode": "Rule Mode", "Global Mode": "Global Mode", "Direct Mode": "Direct Mode", "Enable Tray Speed": "Tray-Geschwindigkeit aktivieren", "Enable Tray Icon": "Tray-Symbol aktivieren", "LightWeight Mode": "Leichtgewichtiger Modus", "LightWeight Mode Info": "GUI-Oberfläche schließen, nur den Kern laufen lassen", "LightWeight Mode Settings": "Einstellungen für den Leichtgewichtigen Modus", "Enter LightWeight Mode Now": "Sofort in den Leichtgewichtigen Modus wechseln", "Auto Enter LightWeight Mode": "Automatisch in den Leichtgewichtigen Modus wechseln", "Auto Enter LightWeight Mode Info": "<PERSON><PERSON> diese Option aktiviert ist, wird der Leichtgewichtige Modus automatisch aktiviert, nachdem das Fenster für eine bestimmte Zeit geschlossen wurde.", "Auto Enter LightWeight Mode Delay": "Verzögerung beim automatischen Wechsel in den Leichtgewichtigen Modus", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "Nach dem Schließen des Fensters wird der Leichtgewichtige Modus automatisch nach {{n}} Minuten aktiviert.", "Config Validation Failed": "Abonnement-Konfigurationsüberprüfung fehlgeschlagen. Bitte überprüfen Sie die Abonnement-Konfigurationsdatei. Die Änderungen wurden rückgängig gemacht. Fehlerdetails: ", "Boot Config Validation Failed": "Start-Abonnement-Konfigurationsüberprüfung fehlgeschlagen. Die Standardkonfiguration wurde verwendet, um die App zu starten. Bitte überprüfen Sie die Abonnement-Konfigurationsdatei. Fehlerdetails: ", "Core Change Config Validation Failed": "Konfigurationsüberprüfung beim Wechsel des Kerns fehlgeschlagen. Die Standardkonfiguration wurde verwendet, um die App zu starten. Bitte überprüfen Sie die Abonnement-Konfigurationsdatei. Fehlerdetails: ", "Config Validation Process Terminated": "Validierungsprozess abgebrochen", "Script Syntax Error": "Skript-Syntaxfehler. Die Änderungen wurden rückgängig gemacht.", "Script Missing Main": "Skriptfehler. Die Änderungen wurden rückgängig gemacht.", "File Not Found": "Datei nicht gefunden. Die Änderungen wurden rückgängig gemacht.", "Script File Error": "Skript-Dateifehler. Die Änderungen wurden rückgängig gemacht.", "Core Changed Successfully": "Kern erfolgreich gewechselt", "Failed to Change Core": "Kernwechsel fehlgeschlagen", "YAML Syntax Error": "YAML-Syntaxfehler. Die Änderungen wurden rückgängig gemacht.", "YAML Read Error": "YAML-Lesefehler. Die Änderungen wurden rückgängig gemacht.", "YAML Mapping Error": "YAML-Mappingfehler. Die Änderungen wurden rückgängig gemacht.", "YAML Key Error": "YAML-Schlüsselfehler. Die Änderungen wurden rückgängig gemacht.", "YAML Error": "YAML-Fehler. Die Änderungen wurden rückgängig gemacht.", "Merge File Syntax Error": "Syntaxfehler in der Überdeckungsdatei. Die Änderungen wurden rückgängig gemacht.", "Merge File Mapping Error": "Mappingfehler in der Überdeckungsdatei. Die Änderungen wurden rückgängig gemacht.", "Merge File Key Error": "Schlüsselfehler in der Überdeckungsdatei. Die Änderungen wurden rückgängig gemacht.", "Merge File Error": "<PERSON><PERSON> in der Überdeckungsdatei. Die Änderungen wurden rückgängig gemacht.", "Service Administrator Prompt": "Clash Verge benö<PERSON>, um den Systemdienst zu installieren.", "DNS Settings": "DNS-Einstellungen", "DNS settings saved": "DNS-Einstellungen wurden gespeichert", "DNS Overwrite": "DNS-Überschreibung", "DNS Settings Warning": "<PERSON>n Si<PERSON> sich nicht mit diesen Einstellungen auskennen, änder<PERSON> Sie sie nicht und lassen Sie die DNS-Überschreibung aktiviert.", "Enable DNS": "DNS aktivieren", "DNS Listen": "DNS-Lauschangabe", "Enhanced Mode": "Erweiterter Modus", "Fake IP Range": "Fake-IP-Bereich", "Fake IP Filter Mode": "Fake-IP-Filtermodus", "Enable IPv6 DNS resolution": "IPv6-DNS-Auflösung aktivieren", "Prefer H3": "HTTP/3 bevorzugen", "DNS DOH使用HTTP/3": "DNS DOH verwendet HTTP/3-Protokoll", "Respect Rules": "Routierungsregeln beachten", "DNS connections follow routing rules": "DNS-Verbindungen folgen den Routierungsregeln", "Use Hosts": "Hosts verwenden", "Enable to resolve hosts through hosts file": "Aktivieren Sie die Auflösung von Hosts über die hosts-Datei", "Use System Hosts": "System-Hosts verwenden", "Enable to resolve hosts through system hosts file": "Aktivieren Sie die Auflösung von Hosts über die System-hosts-Datei", "Direct Nameserver Follow Policy": "Direkte Namenserver folgen der Strategie", "Whether to follow nameserver policy": "Ob die Namenserver-Strategie befolgt werden soll", "Default Nameserver": "Standard-Namenserver", "Default DNS servers used to resolve DNS servers": "Standard-DNS-Server, die zum Aufl<PERSON>sen von DNS-Servern verwendet werden", "Nameserver": "Namenserver", "List of DNS servers": "Liste der DNS-Server, getrennt durch <PERSON>s", "Fallback": "Rückfallserver", "List of fallback DNS servers": "Liste der Rückfall-DNS-Server, getrennt durch <PERSON>mmas", "Proxy Server Nameserver": "Proxy-Server-Namenserver", "Proxy Node Nameserver": "Proxy-Knoten-<PERSON><PERSON><PERSON><PERSON>, nur für die Auflösung der Domains von Proxy-Knoten verwendet, getren<PERSON> durch <PERSON>s", "Direct Nameserver": "Direkter Namenserver", "Direct outbound Nameserver": "Direkter Ausgangs-Namenserver, unterstützt das Schlüsselwort system, getrennt durch Kommas", "Fake IP Filter": "Fake-IP-Filter", "Domains that skip fake IP resolution": "Domains, die die Fake-IP-Auflösung überspringen, getrennt durch Kommas", "Nameserver Policy": "Namenserver-Strategie", "Domain-specific DNS server": "Domain-spezifi<PERSON> DNS-Server, mehrere Server getrennt durch Semikolons, Format: domain=server1;server2", "Fallback Filter Settings": "Rückfallfilter-Einstellungen", "GeoIP Filtering": "GeoIP-Filterung", "Enable GeoIP filtering for fallback": "GeoIP-Rückfallfilterung aktivieren", "GeoIP Code": "GeoIP-Ländercode", "Fallback IP CIDR": "Rückfall-IP-CIDR", "IP CIDRs not using fallback servers": "IP-<PERSON><PERSON><PERSON><PERSON>, die keine Rückfallserver verwenden, getrennt durch <PERSON>mmas", "Fallback Domain": "Rückfall-Domäne", "Domains using fallback servers": "Domains, die Rückfallserver verwenden, getrennt durch Kommas", "Hosts Settings": "Hosts-Einstellungen", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "Benutzerdefinierte Zuordnung von Domains zu IPs oder Domains, getrennt durch Kommas", "Home Settings": "Startseite-Einstellungen", "Profile Card": "Abonnement-Karte", "Current Proxy Card": "Aktueller Proxy-Karte", "Network Settings Card": "Netzwerkeinstellungen-Karte", "Proxy Mode Card": "Proxy-Modus-Karte", "Traffic Stats Card": "Verkehrsstatistik-Karte", "Clash Info Cards": "Clash-Informationen-<PERSON><PERSON>", "System Info Cards": "Systeminformationen-Karten", "Website Tests Card": "Website-Tests-<PERSON><PERSON>", "Traffic Stats": "Verkehrsstatistik", "Website Tests": "Website-Tests", "Clash Info": "Clash-Informationen", "Core Version": "Kernversion", "System Proxy Address": "Systemproxy-Adresse", "Uptime": "Laufzeit", "Rules Count": "Anzahl der Regeln", "System Info": "Systeminformationen", "OS Info": "Betriebssysteminformationen", "Running Mode": "Betriebsmodus", "Sidecar Mode": "Benutzermodus", "Administrator Mode": "Administrator-<PERSON><PERSON>", "Last Check Update": "Letzte Aktualitätsprüfung", "Click to import subscription": "<PERSON><PERSON><PERSON> hier, um ein Abonnement zu importieren.", "Last Update failed": "Letzte Aktualisierung fehlgeschlagen", "Next Up": "Nächste Aktualisierung", "No schedule": "<PERSON><PERSON>", "Unknown": "Unbekannt", "Auto update disabled": "Automatische Aktualisierung deaktiviert", "Update subscription successfully": "Abonnement erfolgreich aktualisiert", "Update failed, retrying with Clash proxy...": "Abonnement-Aktualisierung fehlgeschlagen. Versuche es mit dem Clash-Proxy erneut...", "Update with Clash proxy successfully": "Aktualisierung mit Clash-Proxy erfolgreich", "Update failed even with Clash proxy": "Aktualisierung auch mit Clash-Proxy fehlgeschlagen", "Profile creation failed, retrying with Clash proxy...": "Erstellung des Abonnements fehlgeschlagen. Versuche es mit dem Clash-Proxy erneut...", "Profile creation succeeded with Clash proxy": "Erstellung des Abonnements mit Clash-Proxy erfolgreich", "Import failed, retrying with Clash proxy...": "Import des Abonnements fehlgeschlagen. Versuche es mit dem Clash-Proxy erneut...", "Profile Imported with Clash proxy": "Abonnement mit Clash-Proxy importiert", "Import failed even with Clash proxy": "Import des Abonnements auch mit Clash-Proxy fehlgeschlagen", "Current Node": "Aktueller Knoten", "No active proxy node": "Kein aktiver Proxy-Knoten", "Network Settings": "Netzwerkeinstellungen", "Proxy Mode": "Proxy-Modus", "Group": "Proxy-Gruppe", "Proxy": "Knoten", "IP Information Card": "IP-Informationen-Karte", "IP Information": "IP-Informationen", "Failed to get IP info": "Abrufen der IP-Informationen fehlgeschlagen", "ISP": "Internetdienstanbieter", "ASN": "Autonomes Systemnummer", "ORG": "Organisation", "Location": "<PERSON><PERSON>", "Timezone": "Zeitzone", "Auto refresh": "Automatische Aktualisierung", "Unlock Test": "Entsperrungstest", "Pending": "Wartend auf Prüfung", "Yes": "Unterstützt", "No": "<PERSON>cht unterstützt", "Failed": "Test fehlgeschlagen", "Completed": "Prüfung abgeschlossen", "Disallowed ISP": "Nicht zugelassener Internetdienstanbieter", "Originals Only": "Nur Original", "Unsupported Country/Region": "Nicht unterstütztes Land/Region", "Controller address copied to clipboard": "API-Port in die Zwischenablage kopiert", "Secret copied to clipboard": "API-Schlüssel in die Zwischenablage kopiert", "Copy to clipboard": "<PERSON><PERSON><PERSON> hier, um zu kopieren", "Port Config": "Port-Konfiguration", "Configuration saved successfully": "Zufalls-Konfiguration erfolgreich gespeichert", "Enable one-click random API port and key. Click to randomize the port and key": "Einstellsichere Zufalls-API-Port- und Schlüsselgenerierung aktivieren. Klicken Sie, um Port und Schlüssel zu randomisieren"}