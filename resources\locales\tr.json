{"millis": "ms", "seconds": "saniye", "mins": "<PERSON><PERSON><PERSON>", "Back": "<PERSON><PERSON>", "Close": "Ka<PERSON><PERSON>", "Cancel": "İptal", "Confirm": "<PERSON><PERSON><PERSON>", "Maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Minimize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Format document": "Belgeyi biçimlendir", "Empty": "Boş", "New": "<PERSON><PERSON>", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON>", "Delete": "Sil", "Enable": "Etkinleştir", "Disable": "Devre Dışı Bırak", "Label-Home": "<PERSON>", "Label-Proxies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Label-Profiles": "Profiller", "Label-Connections": "Bağlantılar", "Label-Rules": "<PERSON><PERSON><PERSON>", "Label-Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Label-Unlock": "Test", "Label-Settings": "<PERSON><PERSON><PERSON>", "Proxies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Proxy Groups": "<PERSON>ek<PERSON>", "Proxy Provider": "Vekil <PERSON>ı<PERSON>ısı", "Proxy Count": "<PERSON>ekil <PERSON>", "Update All": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rule": "kural", "global": "<PERSON><PERSON><PERSON><PERSON>", "direct": "<PERSON><PERSON><PERSON><PERSON>", "script": "betik", "locate": "konum", "Delay check": "Gecikme kontrolü", "Sort by default": "Vars<PERSON>ılana gö<PERSON> s<PERSON>", "Sort by delay": "Gecikmeye göre sırala", "Sort by name": "<PERSON><PERSON><PERSON>", "Delay check URL": "Gecikme kontrol URL'si", "Delay check to cancel fixed": "Sabit iptali için gecikme kontrolü", "Proxy basic": "<PERSON>mel Vekil", "Proxy detail": "<PERSON><PERSON><PERSON>", "Profiles": "Profiller", "Update All Profiles": "<PERSON><PERSON><PERSON>", "View Runtime Config": "Çalışma Zamanı Yapılandırmasını Görüntüle", "Reactivate Profiles": "<PERSON><PERSON><PERSON>", "Paste": "Yapıştır", "Profile URL": "Profil U<PERSON>'si", "Import": "İçe Aktar", "From": "<PERSON><PERSON><PERSON>", "Update Time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Used / Total": "Kullanılan / Toplam", "Expire Time": "<PERSON><PERSON>", "Create Profile": "<PERSON><PERSON>", "Edit Profile": "<PERSON><PERSON>", "Edit Proxies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Use newlines for multiple uri": "Birden fazla URI için yeni satırlar kullanın (Base64 kodlaması desteklenir)", "Edit Rules": "Kuralları Düzenle", "Rule Type": "Kural Tipi", "Rule Content": "Kural İçeriği", "Proxy Policy": "Vekil <PERSON>", "No Resolve": "Çözümleme Yok", "Prepend Rule": "<PERSON><PERSON><PERSON><PERSON>", "Append Rule": "<PERSON><PERSON><PERSON><PERSON>", "Prepend Group": "Grubun Ba<PERSON>", "Append Group": "Grubun <PERSON>", "Prepend Proxy": "Vekil'in Başına Ekle", "Append Proxy": "Vekil'in Sonuna Ekle", "Rule Condition Required": "Kural Koşulu Gerekli", "Invalid Rule": "Geçersiz <PERSON>", "Advanced": "Gelişmiş", "Visualization": "Görselleştirme", "DOMAIN": "<PERSON>an adı<PERSON>ş<PERSON>", "DOMAIN-SUFFIX": "<PERSON> adı soneki<PERSON>", "DOMAIN-KEYWORD": "<PERSON> adı anahtar kelimesiyle eşleşir", "DOMAIN-REGEX": "<PERSON> adını düzenli ifadeler kullanarak eşleştirir", "GEOSITE": "Geosite içindeki alan ad<PERSON>ı<PERSON> eşleşir", "GEOIP": "IP adresinin ülke koduyla eşleşir", "SRC-GEOIP": "Kaynak IP adresinin ülke koduyla eşleşir", "IP-ASN": "IP adresinin ASN'siyle eşleşir", "SRC-IP-ASN": "Kaynak IP adresinin ASN'siyle eşleşir", "IP-CIDR": "IP adresi aralığıyla eşleşir", "IP-CIDR6": "IPv6 adresi aralığıyla eşleşir", "SRC-IP-CIDR": "Kaynak IP adresi aralığıyla eşleşir", "IP-SUFFIX": "IP adresi sonek aralığıyla eşleşir", "SRC-IP-SUFFIX": "Kaynak IP adresi sonek aralığıyla eşleşir", "SRC-PORT": "Kaynak port aralığıyla eşleşir", "DST-PORT": "Hedef port aralığıyla eşleşir", "IN-PORT": "Gelen port ile eşleşir", "DSCP": "DSCP işaretlemesi (sadece tvekil UDP girişi için)", "PROCESS-NAME": "İşlem adıyla eşleşir (Android paket adı)", "PROCESS-PATH": "Tam işlem yoluyla eşleşir", "PROCESS-NAME-REGEX": "Tam işlem adını düzenli ifadeler kullanarak eşleştirir (Android paket adı)", "PROCESS-PATH-REGEX": "Tam iş<PERSON> yolunu d<PERSON> ifadeler kullanarak eşleştirir", "NETWORK": "Taşıma protokolüyle eşleşir (tcp/udp)", "UID": "Linux KULLANICI ID'<PERSON><PERSON>", "IN-TYPE": "Gelen bağlantı tipiyle eşleşir", "IN-USER": "Gelen bağlantı kullanıcı adıyla eşleşir", "IN-NAME": "Gelen bağlantı adıyla eşleşir", "SUB-RULE": "Alt kural", "RULE-SET": "<PERSON><PERSON>iyle <PERSON>", "AND": "Mantıksal VE", "OR": "Mantıksal VEYA", "NOT": "Mantıksal DEĞİL", "MATCH": "<PERSON><PERSON>m isteklerle eşleşir", "DIRECT": "<PERSON><PERSON> doğ<PERSON> dı<PERSON> gider", "REJECT": "İ<PERSON><PERSON><PERSON>", "REJECT-DROP": "İstekleri atar", "PASS": "Eşleştiğinde bu kuralı atlar", "Edit Groups": "Vekil Gruplarını Düzenle", "Group Type": "Grup Tipi", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON> manuel o<PERSON>", "url-test": "URL testi gecikmesine göre vekil seçin", "fallback": "Hata durumunda başka bir vekil'e geçin", "load-balance": "Yük dengelemeye göre vekil dağıtın", "relay": "Tanımlanan vekil zincirinden geçirin", "Group Name": "Grup Adı", "Use Proxies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Use Provider": "Sağlayıcı Kullan", "Health Check Url": "Sağlık Kontrolü URL'si", "Expected Status": "Beklenen Durum", "Interval": "Aralık", "Lazy": "Tembel", "Timeout": "Zaman Aşımı", "Max Failed Times": "<PERSON><PERSON><PERSON><PERSON> Başarısız Deneme", "Interface Name": "Arayüz Adı", "Routing Mark": "Yönlendirme İşareti", "Include All": "Tüm Vekil'leri ve Sağlayıcıları Dahil Et", "Include All Providers": "<PERSON><PERSON>m Sağlayıcıları Dahil Et", "Include All Proxies": "<PERSON><PERSON><PERSON>", "Exclude Filter": "<PERSON><PERSON>", "Exclude Type": "<PERSON><PERSON>", "Disable UDP": "UDP'yi Devre Dışı Bırak", "Hidden": "<PERSON><PERSON><PERSON>", "Group Name Required": "Grup Adı Gerekli", "Group Name Already Exists": "Grup Adı Zaten Var", "Extend Config": "Yapılandırma <PERSON>me", "Extend Script": "Betik <PERSON>işletme", "Global Merge": "Küresel Yapılandırma Genişletme", "Global Script": "Küresel Betik Genişletme", "Type": "Tip", "Name": "İsim", "Descriptions": "Açıklamalar", "Subscription URL": "Abonelik URL'si", "Update Interval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Choose File": "<PERSON><PERSON><PERSON>", "Use System Proxy": "Sistem Vekil'ini <PERSON>", "Use Clash Proxy": "<PERSON><PERSON><PERSON><PERSON>", "Accept Invalid Certs (Danger)": "Geçersiz Sertifikalara İzin Ver (Tehlikeli)", "Refresh": "<PERSON><PERSON><PERSON>", "Home": "<PERSON>", "Select": "Seç", "Edit Info": "Bilgileri Düzenle", "Edit File": "Dosyayı Düzenle", "Open File": "Dosyayı Aç", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update(Proxy)": "Güncelle(Vekil)", "Confirm deletion": "<PERSON><PERSON><PERSON><PERSON>", "This operation is not reversible": "Bu işlem geri alınamaz", "Script Console": "<PERSON><PERSON>", "To Top": "Başa <PERSON>", "To End": "<PERSON><PERSON>", "Connections": "Bağlantılar", "Table View": "<PERSON><PERSON><PERSON>", "List View": "Liste Görünümü", "Close All": "Tümünü <PERSON>", "Upload": "<PERSON><PERSON><PERSON><PERSON>", "Download": "İndirme", "Download Speed": "İndirme Hızı", "Upload Speed": "Yükleme Hızı", "Host": "<PERSON>", "Downloaded": "İndirilen", "Uploaded": "<PERSON><PERSON><PERSON><PERSON>", "DL Speed": "İndirme Hızı", "UL Speed": "Yükleme Hızı", "Active Connections": "<PERSON><PERSON><PERSON>", "Chains": "Zincirler", "Rule": "<PERSON><PERSON>", "Process": "İşlem", "Time": "Zaman", "Source": "<PERSON><PERSON><PERSON>", "Destination": "<PERSON><PERSON><PERSON>", "DestinationPort": "Hedef Port", "Close Connection": "Bağlantıyı Kapat", "Rules": "<PERSON><PERSON><PERSON>", "Rule Provider": "Kural Sağlayıcısı", "Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pause": "<PERSON><PERSON><PERSON>", "Resume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear": "<PERSON><PERSON><PERSON>", "Test": "Test", "Test All": "Tümünü Test Et", "Testing...": "Test Ediliyor...", "Create Test": "Test Oluştur", "Edit Test": "<PERSON><PERSON>", "Icon": "<PERSON>m<PERSON>", "Test URL": "Test URL'si", "Settings": "<PERSON><PERSON><PERSON>", "System Setting": "Sistem Ayarları", "Tun Mode": "<PERSON><PERSON>", "TUN requires Service Mode": "TUN modu hizmet kurulumu gerektirir", "Install Service": "Hizmeti Kur", "Install Service failed": "Hizmet kurulumu başarısız oldu", "Restart Core failed": "Çekirdek yeniden başlatma başarısız oldu", "Reset to Default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tun Mode Info": "Tun (Sanal Ağ Kartı) modu: Tüm sistem trafiğ<PERSON> ya<PERSON>, etkinleştirildiğinde sistem vekil'ini etkinleştirmeye gerek yoktur.", "System Proxy Enabled": "Sistem vekil'i etkinleştirildi, uygulamalarınız vekil üzerinden ağa erişecek", "System Proxy Disabled": "Sistem vekil'i devre dışı, çoğu kullanıcı için bu seçeneği açmanız önerilir", "TUN Mode Enabled": "TUN modu etkin<PERSON>ş<PERSON>di, uygulamalar sanal ağ kartı üzerinden ağa erişecek", "TUN Mode Disabled": "TUN modu devre dışı, özel uygulamalar için uygundur", "TUN Mode Service Required": "TUN modu hizmet modu gerektirir, lütfen önce hizmeti kurun", "TUN Mode Intercept Info": "TUN modu tüm uygulama trafiğini ele alabilir, sistem vekil ayarlarını takip etmeyen özel uygulamalar için uygundur", "Rule Mode Description": "Trafiği önceden ayarlanmış kurallara göre yönlendirir, esnek vekil stratejileri sağlar", "Global Mode Description": "Tüm trafik vekil sunucuları üzerinden geçer, küresel internet erişimi gerektiren senaryolar için uygundur", "Direct Mode Description": "Tüm trafik vekil düğümleri üzerinden geçmez, ancak Clash çekirdeği tarafından hedef sunuculara yönlendirilir, çekirdek trafik dağıtımı gerektiren özel senaryolar için uygundur", "Stack": "Tun <PERSON>ını", "System and Mixed Can Only be Used in Service Mode": "Sistem ve Karışık Modlar Sadece Hizmet Modunda Ku<PERSON>ılabilir", "Device": "Cihaz Adı", "Auto Route": "Otomatik Yönlendirme", "Strict Route": "Katı Yönlendirme", "Auto Detect Interface": "Arayüzü Otomatik Algıla", "DNS Hijack": "DNS Ele Geçirme", "MTU": "<PERSON><PERSON><PERSON><PERSON>", "Service Mode": "<PERSON>zmet <PERSON>", "Service Mode Info": "TUN modunu etkinleştirmeden önce lütfen hizmet modunu kurun. Hizmet tarafından başlatılan çekirdek işlemi, sanal ağ kartını (TUN modu) kurma iznini alabilir", "Current State": "Mevcut Durum", "pending": "be<PERSON><PERSON><PERSON><PERSON>", "installed": "kuruldu", "uninstall": "kaldırıldı", "active": "aktif", "unknown": "bilinmiyor", "Information: Please make sure that the Clash Verge Service is installed and enabled": "Bilgi: Lütfen Clash Verge Hizmeti'nin kurulu ve etkin olduğundan emin olun", "Install": "<PERSON><PERSON>", "Uninstall": "Kaldır", "Disable Service Mode": "Hizmet Modunu Devre Dışı Bırak", "System Proxy": "Sistem Vekil'i", "System Proxy Info": "İşletim sisteminin vekil ayarlarını değiştirmek için etkinleştirin. Etkinleştirme başarısız olursa, işletim sisteminin proxy ayarlarını manuel o<PERSON> değiştirin", "System Proxy Setting": "Sistem Vekil Ayarı", "Current System Proxy": "Geçerli Sistem Vekil'i", "Enable status": "Etkinlik Durumu:", "Enabled": "<PERSON><PERSON><PERSON>", "Disabled": "Devre Dışı", "Server Addr": "<PERSON><PERSON><PERSON>: ", "Proxy Host": "<PERSON><PERSON><PERSON>", "Invalid Proxy Host Format": "Geçersiz Vekil Sunucusu Formatı", "Not available": "Kullanılamıyor", "Proxy Guard": "<PERSON><PERSON><PERSON>", "Proxy Guard Info": "<PERSON>ğer yazılımların işletim sisteminin vekil ayarlarını değiştirmesini önlemek için etkinleştirin", "Guard Duration": "<PERSON><PERSON><PERSON>", "Always use Default Bypass": "Her Zaman Varsayılan Baypas Kullan", "Use Bypass Check": "Baypas Kontrolü <PERSON>", "Proxy Bypass": "Vekil Baypas Ayarları: ", "Bypass": "Baypas: ", "Use PAC Mode": "PAC <PERSON><PERSON><PERSON>", "PAC Script Content": "PAC Betiği İçeriği", "PAC URL": "PAC URL'si: ", "Auto Launch": "Otomatik Başlatma", "Administrator mode may not support auto launch": "Yönetici modu otomatik başlatmayı desteklemeyebilir", "Silent Start": "<PERSON><PERSON><PERSON>", "Silent Start Info": "Programı paneli görüntülemeden arka plan modunda başlatır", "TG Channel": "Telegram Kanalı", "Manual": "Kılavuz", "Github Repo": "<PERSON><PERSON><PERSON>", "Clash Setting": "Clash Ayarı", "Allow Lan": "LAN'a İzin Ver", "Network Interface": "Ağ Arayüzü", "Ip Address": "IP Adresi", "Mac Address": "MAC Adresi", "IPv6": "IPv6", "Unified Delay": "Birleşik Gecikme", "Unified Delay Info": "Birleşik gecikme açıldığında, bağlantı el sıkışmaları vb. nedeniyle farklı tür düğümler arasındaki gecikme farklarını ortadan kaldırmak için iki gecikme testi gerçekleştirilir", "Log Level": "Günlük Seviyesi", "Log Level Info": "Bu parametre yalnızca günlük dizini Hizmet klasöründeki çekirdek günlük dosyaları için geçerlidir", "Port Config": "Port Yapılandırması", "Random Port": "Rastgele Port", "Mixed Port": "Karışık Port", "Socks Port": "Socks Portu", "Http Port": "Http(s) Portu", "Redir Port": "Redir Portu", "Tproxy Port": "Tvekil Portu", "External": "<PERSON><PERSON>", "External Controller": "<PERSON><PERSON>", "Core Secret": "Çekirdek <PERSON>ırrı", "Recommended": "Önerilen", "Open URL": "URL Aç", "Replace host, port, secret with %host, %port, %secret": "Ana bilgisayar, port, sırrı %host, %port, %secret ile değiştirin", "Support %host, %port, %secret": "%host, %port, %secret destekler", "Clash Core": "<PERSON><PERSON>", "Upgrade": "Yükselt", "Restart": "<PERSON><PERSON><PERSON>", "Release Version": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Alpha Version": "Alfa Versiyonu", "Please Enable Service Mode": "Lütfen Önce Hizmet Modunu <PERSON> ve Etkinleştirin", "Please enter your root password": "Lütfen root şifrenizi girin", "Grant": "<PERSON><PERSON> V<PERSON>", "Open UWP tool": "UWP Aracını Aç", "Open UWP tool Info": "Windows 8'den beri, UWP uygulamaları (Microsoft Store gibi) yerel ana bilgisayar ağ hizmetlerine doğrudan erişmekten kısıtlanmıştır ve bu araç bu kısıtlamayı atlatmak için kullanılabilir", "Update GeoData": "GeoData'y<PERSON>", "Verge Basic Setting": "Verge Te<PERSON> Ayarı", "Verge Advanced Setting": "Verge G<PERSON>ş Ayarı", "Language": "Dil", "Theme Mode": "<PERSON><PERSON>", "theme.light": "Açık", "theme.dark": "<PERSON><PERSON>", "theme.system": "Sistem", "Tray Click Event": "Sistem Tepsisi Tıklama Olayı", "Show Main Window": "<PERSON>", "Show Tray Menu": "<PERSON><PERSON><PERSON>ü<PERSON>", "Copy Env Type": "Env <PERSON><PERSON><PERSON>", "Copy Success": "Kopyalama Başarılı", "Start Page": "Başlangıç <PERSON>ı", "Startup Script": "Başlangıç <PERSON>ği", "Browse": "<PERSON><PERSON><PERSON><PERSON>", "Theme Setting": "Tema Ayarı", "Primary Color": "<PERSON>", "Secondary Color": "İkincil Renk", "Primary Text": "<PERSON>", "Secondary Text": "<PERSON><PERSON><PERSON><PERSON>", "Info Color": "<PERSON><PERSON><PERSON>", "Warning Color": "Uyarı Rengi", "Error Color": "<PERSON><PERSON>", "Success Color": "Başarı Rengi", "Font Family": "Yazı Tipi Ailesi", "CSS Injection": "CSS Enjeksiyonu", "Layout Setting": "Düzen Ayarı", "Traffic Graph": "Trafik <PERSON>i", "Memory Usage": "<PERSON><PERSON><PERSON><PERSON>", "Memory Cleanup": "Belleği temizlemek için dokunun", "Proxy Group Icon": "Vekil Grup Simgesi", "Nav Icon": "<PERSON><PERSON><PERSON><PERSON>", "Monochrome": "Tek Renkli", "Colorful": "<PERSON><PERSON><PERSON>", "Tray Icon": "Tepsi Simgesi", "Common Tray Icon": "Genel Tepsi Simgesi", "System Proxy Tray Icon": "Sistem Vekil Tepsi Simgesi", "Tun Tray Icon": "<PERSON>n <PERSON> Simgesi", "Miscellaneous": "Çeşitli", "App Log Level": "Uygulama Günlük Seviyesi", "Auto Close Connections": "Bağlantıları Otomatik Kapat", "Auto Close Connections Info": "Vekil grup seçimi veya vekil modu değiştiğinde kurulan bağlantıları sonlandır", "Auto Check Update": "Otomatik Güncelleme Kontrolü", "Enable Builtin Enhanced": "Yerleşik Geliştirilmiş Modu Etkinleştir", "Enable Builtin Enhanced Info": "Yapılandırma dosyası için uyumluluk işleme", "Proxy Layout Columns": "Vekil Düzeni <PERSON>ı", "Auto Columns": "Otomatik Sütunlar", "Auto Log Clean": "Otomatik Günlük Temizleme", "Never Clean": "<PERSON><PERSON>", "Retain _n Days": "{{n}} <PERSON><PERSON><PERSON>", "Default Latency Test": "Varsayılan Gecikme Testi", "Default Latency Test Info": "Yalnızca HTTP istemci isteği testi için kullanılır ve yapılandırma dosyasında bir fark yaratmaz", "Default Latency Timeout": "Varsayılan Gecikme Zaman Aşımı", "Hotkey Setting": "Kısayol Tuşu Ayarı", "Enable Global Hotkey": "Küresel Kısayol Tuşunu Etkinleştir", "open_or_close_dashboard": "Kontrol Panelini Aç/Kapat", "clash_mode_rule": "Ku<PERSON> Modu", "clash_mode_global": "<PERSON><PERSON><PERSON><PERSON> Mod", "clash_mode_direct": "<PERSON><PERSON><PERSON><PERSON>", "toggle_system_proxy": "Sistem Vekil'ini Etkinleştir/Devre Dışı Bırak", "toggle_tun_mode": "<PERSON>n <PERSON>ştir/Devre Dışı Bırak", "entry_lightweight_mode": "<PERSON><PERSON><PERSON>", "Backup Setting": "Yedekleme A<PERSON>ı", "Backup Setting Info": "WebDAV yedekleme yapılandırma dosyalarını destekler", "Runtime Config": "Çalışma Zamanı Yapılandırması", "Open Conf Dir": "Yapılandırma Dizinini Aç", "Open Conf Dir Info": "<PERSON><PERSON><PERSON><PERSON>ım anormal çalışıyorsa, bu klasördeki tüm dosyaları YEDEKLEYİN ve silin, ard<PERSON><PERSON><PERSON> yazılımı yeniden başlatın", "Open Core Dir": "Çek<PERSON><PERSON> Dizinini <PERSON>", "Open Logs Dir": "Günlük Dizinini Aç", "Check for Updates": "Güncellemeleri Kontrol Et", "Go to Release Page": "<PERSON><PERSON><PERSON><PERSON>m <PERSON>fa<PERSON>ına Git", "Portable Updater Error": "Taşınabilir sürüm uygulama içi güncellemeleri desteklemez. Lütfen manuel o<PERSON>ak indirip <PERSON>ğiştirin", "Break Change Update Error": "<PERSON>u sürüm büyük bir güncellemedir ve uygulama içi güncellemeleri desteklemez. Lütfen kaldırın ve yeni sürümü manuel olarak indirip kurun", "Open Dev Tools": "Geliştirici Araçları", "Export Diagnostic Info": "Tanılama Bilgilerini Dışa Aktar", "Export Diagnostic Info For Issue Reporting": "<PERSON><PERSON> Bildirimi İçin Tanılama Bilgilerini Dışa Aktar", "Exit": "Çıkış", "Verge Version": "<PERSON>erge <PERSON>ü<PERSON>ü<PERSON>", "ReadOnly": "Salt Okunur", "ReadOnlyMessage": "Salt okunur düzenleyicide düzenlenemez", "Filter": "Filtre", "Filter conditions": "Filtre koşulları", "Match Case": "Büyük/Küçük Harf Eşleştir", "Match Whole Word": "<PERSON>", "Use Regular Expression": "Düzenli İfade Kullan", "Profile Imported Successfully": "Profil Başarıyla İçe Aktarıldı", "Profile Switched": "<PERSON><PERSON>", "Profile Reactivated": "<PERSON><PERSON>", "Only YAML Files Supported": "Yalnızca YAML Dosyaları Desteklenir", "Settings Applied": "Ayarlar Uygulandı", "Installing Service...": "<PERSON>zmet <PERSON>...", "Service Installed Successfully": "Hizmet Başarıyla Kuruldu", "Service Uninstalled Successfully": "Hizmet Başarıyla Kaldırıldı", "Proxy Daemon Duration Cannot be Less than 1 Second": "Vekil Koruyucu Süresi 1 Saniyeden Az Olamaz", "Invalid Bypass Format": "Geçersiz Baypas Formatı", "Clash Port Modified": "Clash Portu Değiştirildi", "Port Conflict": "Port Çakışması", "Restart Application to Apply Modifications": "Değişiklikleri Uygulamak İçin Uygulamayı Yeniden Başlatın", "External Controller Address Modified": "Harici Denetleyici Adresi Değiştirildi", "Permissions Granted Successfully for _clash Core": "{{core}} Çekirdeği İçin İzinler Başarıyla Verildi", "Core Version Updated": "Çekirdek Sürümü Güncellendi", "Clash Core Restarted": "Clash Çekirdeği Yeniden Başlatıldı", "GeoData Updated": "GeoData <PERSON>di", "Currently on the Latest Version": "Şu Anda En Son Sürümdesiniz", "Import Subscription Successful": "Abonelik içe aktarımı başarılı", "WebDAV Server URL": "WebDAV Sunucu URL'si", "Username": "Kullanıcı Adı", "Password": "Şifre", "Backup": "<PERSON><PERSON><PERSON>", "Filename": "<PERSON><PERSON><PERSON>", "Actions": "İşlemler", "Restore": "<PERSON><PERSON>", "No Backups": "Kullanılabilir yedek yok", "WebDAV URL Required": "WebDAV URL'si boş olamaz", "Invalid WebDAV URL": "Geçersiz WebDAV URL formatı", "Username Required": "Kullanıcı adı boş olamaz", "Password Required": "<PERSON><PERSON><PERSON> bo<PERSON>", "Failed to Fetch Backups": "<PERSON><PERSON>yaları alınamadı", "WebDAV Config Saved": "WebDAV yapılandırması başarıyla kaydedildi", "WebDAV Config Save Failed": "WebDAV yapılandırması kaydedilemedi: {{error}}", "Backup Created": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Backup Failed": "Ye<PERSON><PERSON>e başarısız oldu: {{error}}", "Delete Backup": "<PERSON><PERSON><PERSON><PERSON>", "Restore Backup": "<PERSON><PERSON><PERSON><PERSON>", "Backup Time": "<PERSON><PERSON><PERSON><PERSON>", "Confirm to delete this backup file?": "Bu yedek dosyasını silmeyi onaylıyor musunuz?", "Confirm to restore this backup file?": "Bu yedek dos<PERSON>ını geri yüklemeyi onaylıyor musunuz?", "Restore Success, App will restart in 1s": "Geri Yükleme Başarılı, Uygulama 1 saniye içinde yeniden başlatılacak", "Failed to fetch backup files": "<PERSON><PERSON>yaları alınamadı", "Profile": "Profil", "Help": "Yardım", "About": "Hakkında", "Theme": "<PERSON><PERSON>", "Main Window": "<PERSON>", "Group Icon": "Grup Simgesi", "Menu Icon": "<PERSON><PERSON>", "PAC File": "PAC Dosyası", "Web UI": "Web Arayüzü", "Hotkeys": "Kısayol Tuşları", "Verge Mixed Port": "Verge Karışık Port", "Verge Socks Port": "Verge Socks Portu", "Verge Redir Port": "Verge Redir Portu", "Verge Tproxy Port": "Verge Tvekil Portu", "Verge Port": "<PERSON><PERSON><PERSON>", "Verge HTTP Enabled": "Verge HTTP <PERSON>", "WebDAV URL": "WebDAV URL'si", "WebDAV Username": "WebDAV Kullanıcı Adı", "WebDAV Password": "WebDAV Şifresi", "Dashboard": "Ko<PERSON>rol <PERSON>i", "Restart App": "Uygulamayı Yeniden Başlat", "Restart Clash Core": "Clash Çekirdeğini Yeniden Başlat", "TUN Mode": "TUN Modu", "Copy Env": "<PERSON><PERSON>", "Conf Dir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Core Dir": "<PERSON><PERSON><PERSON><PERSON>", "Logs Dir": "Günlük Dizini", "Open Dir": "<PERSON><PERSON>", "More": "<PERSON><PERSON>", "Rule Mode": "Ku<PERSON> Modu", "Global Mode": "<PERSON><PERSON><PERSON><PERSON> Mod", "Direct Mode": "<PERSON><PERSON><PERSON><PERSON>", "Enable Tray Speed": "Tepsi Hızını Etkinleştir", "Enable Tray Icon": "Tepsi Simgesini Etkinleştir", "LightWeight Mode": "<PERSON><PERSON><PERSON>", "LightWeight Mode Info": "GUI'yi kapatın ve yalnızca çekirdeği çalışır durumda tutun", "LightWeight Mode Settings": "<PERSON><PERSON><PERSON>", "Enter LightWeight Mode Now": "<PERSON><PERSON><PERSON>", "Auto Enter LightWeight Mode": "Otomatik Ha<PERSON>", "Auto Enter LightWeight Mode Info": "<PERSON><PERSON>e belirli bir süre kapatıldıktan sonra <PERSON> otomatik olarak etkinleştirilmesi için etkinleştirin", "Auto Enter LightWeight Mode Delay": "Otomatik Hafi<PERSON>", "When closing the window, LightWeight Mode will be automatically activated after _n minutes": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> {{n}} dakika sonra otomatik olarak etkinleştirilecek", "Config Validation Failed": "Abonelik yapılandırması doğrulaması başarısız oldu. Lütfen abonelik yapılandırma dosyasını kontrol edin; değişiklikler geri alındı.", "Boot Config Validation Failed": "Başlangıç abonelik yapılandırması doğrulaması başarısız oldu. Varsayılan yapılandırma ile başlatıldı; lütfen abonelik yapılandırma dosyasını kontrol edin.", "Core Change Config Validation Failed": "Çekirdek değiştirilirken yapılandırma doğrulaması başarısız oldu. Varsayılan yapılandırma ile başlatıldı; lütfen abonelik yapılandırma dosyasını kontrol edin.", "Config Validation Process Terminated": "Doğrulama işlemi sonlandırıldı.", "Script Syntax Error": "Betik sözdizimi hat<PERSON>ı, değişiklikler geri alı<PERSON>ı", "Script Missing Main": "Betik <PERSON>ı, değişiklikler geri alındı", "File Not Found": "<PERSON><PERSON><PERSON>, değişiklikler geri alındı", "Script File Error": "Betik dosyası hatası, değişiklikler geri alındı", "Core Changed Successfully": "Çekirdek başarıyla değiştirildi", "Failed to Change Core": "<PERSON><PERSON><PERSON><PERSON>ril<PERSON>edi", "YAML Syntax Error": "YAML sözdizimi hat<PERSON>ı, değişiklikler geri alındı", "YAML Read Error": "YAML okuma hatası, değişiklikler geri alındı", "YAML Mapping Error": "YAML eşleme <PERSON>, değişiklikler geri alındı", "YAML Key Error": "YAML anah<PERSON>, değişiklikler geri al<PERSON>ı", "YAML Error": "YAML <PERSON>, değişiklikler geri alındı", "Merge File Syntax Error": "Birleştirme dosyası sözdizimi hatası, değişiklikler geri alındı", "Merge File Mapping Error": "Birleştirme dosyası eşleme hatası, değişiklikler geri alındı", "Merge File Key Error": "Birleştirme dosyası anahtar hatası, değişiklikler geri alındı", "Merge File Error": "Birleştirme dosyası hatası, değişiklikler geri alındı", "Validate YAML File": "YAML Dosyasını Doğrula", "Validate Merge File": "Birleştirme Dosyasını Doğrula", "Validation Success": "Doğrulama Başarılı", "Validation Failed": "Doğrulama Başarısız", "Service Administrator Prompt": "Clash Verge sistem hizmetini yeniden kurmak için yönetici ayrıcalıkları gerektiriyor", "DNS Settings": "DNS Ayarları", "DNS settings saved": "DNS ayarları kaydedildi", "DNS Overwrite": "DNS Üzerine Yazma", "DNS Settings Warning": "Bu ayarlarla ilgili bilginiz yoksa, lütfen bunları değiştirmeyin ve DNS Üzerine Yazma'yı etkin tutun", "Enable DNS": "DNS'i Etkinleştir", "DNS Listen": "DNS <PERSON>", "Enhanced Mode": "Geliştirilmiş <PERSON>", "Fake IP Range": "Sahte IP Aralığı", "Fake IP Filter Mode": "Sahte IP Filtre Modu", "Enable IPv6 DNS resolution": "IPv6 DNS çözümlemesini etkinleştir", "Prefer H3": "H3'ü Tercih Et", "DNS DOH uses HTTP/3": "DNS DOH HTTP/3 kullanır", "Respect Rules": "<PERSON><PERSON><PERSON><PERSON>", "DNS connections follow routing rules": "DNS bağlantıları yönlendirme kurallarını takip eder", "Use Hosts": "Hosts <PERSON>", "Enable to resolve hosts through hosts file": "Ana bilgisayarları hosts dosyası aracılığıyla çözümlemek için etkinleştirin", "Use System Hosts": "Sistem Hosts Dosyasını Kullan", "Enable to resolve hosts through system hosts file": "Ana bilgisayarları sistem hosts dosyası aracılığıyla çözümlemek için etkinleştirin", "Direct Nameserver Follow Policy": "<PERSON><PERSON><PERSON>an İsim Sunucusu Politikasını Takip Et", "Whether to follow nameserver policy": "İsim sunucusu politikasının takip edilip edilmeyeceği", "Default Nameserver": "Varsayılan İsim <PERSON>uc<PERSON>u", "Default DNS servers used to resolve DNS servers": "DNS sunucularını çözümlemek için kullanılan varsayılan DNS sunucuları", "Nameserver": "<PERSON><PERSON><PERSON>", "List of DNS servers": "DNS sunucuları listesi, virgülle ayrılmış", "Fallback": "<PERSON><PERSON>", "List of fallback DNS servers": "Yedek DNS sunucuları listesi, virgülle ayrılmış", "Proxy Server Nameserver": "<PERSON><PERSON><PERSON>", "Proxy Node Nameserver": "Vekil düğümü alan adı <PERSON>özümlemesi için DNS sunucuları", "Direct Nameserver": "<PERSON><PERSON><PERSON><PERSON>", "Direct outbound Nameserver": "<PERSON><PERSON><PERSON><PERSON> alan adı <PERSON>özümlemesi için DNS sunucuları, 'system' anahtar kelimesini destekler, virgülle ayrılmış", "Fake IP Filter": "Sahte IP Filtresi", "Domains that skip fake IP resolution": "Sahte IP çözümlemesini atlayan alan ad<PERSON>, virgülle ayrılmış", "Nameserver Policy": "İsim Sunucusu Politikası", "Domain-specific DNS server": "Alana özgü DNS sunucusu, birden çok sunucu noktalı virgülle ayrılır, format: domain=server1;server2", "Fallback Filter Settings": "<PERSON><PERSON>", "GeoIP Filtering": "GeoIP Filtreleme", "Enable GeoIP filtering for fallback": "<PERSON><PERSON>in GeoIP filtrelemeyi etkinleştir", "GeoIP Code": "GeoIP Kodu", "Fallback IP CIDR": "Yedek IP CIDR", "IP CIDRs not using fallback servers": "<PERSON>dek sunucuları kullanmayan IP CIDR'ları, virgülle ayrılmış", "Fallback Domain": "<PERSON><PERSON>", "Domains using fallback servers": "<PERSON><PERSON> sun<PERSON>ı kullanan alan <PERSON>, vir<PERSON><PERSON><PERSON> ayrılmı<PERSON>", "Hosts Settings": "Hosts <PERSON>", "Hosts": "Hosts", "Custom domain to IP or domain mapping": "<PERSON><PERSON> alan adından IP'ye veya alan adına eşleme", "Enable Alpha Channel": "Alfa Kanalını Etkinleştir", "Alpha versions may contain experimental features and bugs": "Alfa sürümleri deneysel özellikler ve hatalar içerebilir", "Home Settings": "Ana <PERSON>", "Profile Card": "<PERSON>il <PERSON>", "Current Proxy Card": "Geçerli Vekil Kartı", "Network Settings Card": "Ağ Ayarları Kartı", "Proxy Mode Card": "Vekil <PERSON> Ka<PERSON>ı", "Clash Mode Card": "Clash Modu Kartı", "Traffic Stats Card": "Trafik İstatistikleri Kartı", "Clash Info Cards": "Clash Bilgi <PERSON>", "System Info Cards": "Sistem Bilgi Ka<PERSON>ları", "Website Tests Card": "Web Sitesi Testleri Kartı", "Traffic Stats": "Trafik İstatistikleri", "Website Tests": "Web Sitesi Testleri", "Clash Info": "<PERSON><PERSON>", "Core Version": "Çekirdek Sürümü", "System Proxy Address": "Sistem Vekil <PERSON>i", "Uptime": "Çalışma Süresi", "Rules Count": "Kural Sayısı", "System Info": "Sistem Bilgisi", "OS Info": "İşletim Sistemi Bilgisi", "Running Mode": "Çalışma Modu", "Sidecar Mode": "Kullanıcı Modu", "Administrator Mode": "Yönetici Modu", "Administrator + Service Mode": "Yönetici + Hizmet Modu", "Last Check Update": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Click to import subscription": "Abonelik içe aktarmak için tıklayın", "Last Update failed": "Son gü<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "Next Up": "<PERSON><PERSON><PERSON><PERSON>", "No schedule": "Program yok", "Unknown": "Bilinmiyor", "Auto update disabled": "Otomatik güncelleme de<PERSON> dışı", "Update subscription successfully": "Abonelik başarıyla güncellendi", "Update failed, retrying with Clash proxy...": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> vekil ile yeniden deneniyor...", "Update with Clash proxy successfully": "<PERSON>lash vekil ile güncelleme başarılı", "Update failed even with Clash proxy": "<PERSON><PERSON> vekil ile bile güncelleme başarısız oldu", "Profile creation failed, retrying with Clash proxy...": "<PERSON>il <PERSON>tur<PERSON> baş<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> vekil ile yeniden deneniyor...", "Profile creation succeeded with Clash proxy": "Clash vekil ile profil oluşturma başarılı oldu", "Import failed, retrying with Clash proxy...": "İçe aktarma başar<PERSON><PERSON><PERSON>z <PERSON>u, <PERSON><PERSON> vekil ile yeniden deneniyor...", "Profile Imported with Clash proxy": "Profil Clash vekil ile içe aktarıldı", "Import failed even with Clash proxy": "Clash vekil ile bile içe aktarma başarısız oldu", "Current Node": "Geçerli <PERSON>ü<PERSON>", "No active proxy node": "Aktif vekil düğümü yok", "Network Settings": "<PERSON><PERSON>", "Proxy Mode": "<PERSON><PERSON><PERSON>", "Group": "Grup", "Proxy": "Vekil", "IP Information Card": "IP Bilgi Kartı", "IP Information": "IP Bilgisi", "Failed to get IP info": "IP bilgisi alınamadı", "ISP": "ISP", "ASN": "ASN", "ORG": "Kuruluş", "Location": "<PERSON><PERSON>", "Timezone": "Saat Dilimi", "Auto refresh": "Otomatik yenile", "Unlock Test": "<PERSON><PERSON>", "Pending": "Beklemede", "Yes": "<PERSON><PERSON>", "No": "Hay<PERSON><PERSON>", "Failed": "Başarısız", "Completed": "Tamamlandı", "Disallowed ISP": "İzin Verilmeyen ISP", "Originals Only": "Yalnızca Orijinaller", "No (IP Banned By Disney+)": "Hayır (IP Disney+ Tarafından Yasaklandı)", "Unsupported Country/Region": "Desteklenmeyen Ülke/Bölge", "Failed (Network Connection)": "Başarısız (Ağ Bağlantısı)"}